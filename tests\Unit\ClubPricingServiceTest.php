<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\ClubPricingService;
use App\Models\HeadDsgn;
use App\Models\GripType;
use App\Models\GripSz;
use App\Models\Loft;
use App\Models\Lie;
use App\Models\FaceAngle;
use App\Models\Hossel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class ClubPricingServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $pricingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->pricingService = new ClubPricingService();
    }

    /** @test */
    public function it_calculates_basic_model_price_for_b2b()
    {
        // Create a test model with B2B pricing
        $model = HeadDsgn::factory()->create([
            'type' => 'b2b',
            'b2b_price' => 100.00,
            'b2c_price' => null
        ]);

        // Create a B2B user (fitter)
        $user = User::factory()->create();
        $user->assignRole('fitter');
        Auth::login($user);

        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => $model->HeadDsgnId
                ]
            ]
        ];

        $price = $this->pricingService->calculateClubPrice($clubData);
        
        $this->assertEquals(100.00, $price);
    }

    /** @test */
    public function it_calculates_basic_model_price_for_b2c()
    {
        // Create a test model with B2C pricing
        $model = HeadDsgn::factory()->create([
            'type' => 'b2c',
            'b2b_price' => null,
            'b2c_price' => 150.00
        ]);

        // Create a B2C user (customer)
        $user = User::factory()->create();
        $user->assignRole('customer');
        Auth::login($user);

        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => $model->HeadDsgnId
                ]
            ]
        ];

        $price = $this->pricingService->calculateClubPrice($clubData);
        
        $this->assertEquals(150.00, $price);
    }

    /** @test */
    public function it_calculates_price_with_optional_components()
    {
        // Create test data
        $model = HeadDsgn::factory()->create([
            'type' => 'both',
            'b2b_price' => 100.00,
            'b2c_price' => 150.00
        ]);

        $gripType = GripType::factory()->create(['price' => 25.00]);
        $gripSize = GripSz::factory()->create(['price' => 10.00]);
        $loft = Loft::factory()->create(['price' => 15.00]);

        // Create a B2B user
        $user = User::factory()->create();
        $user->assignRole('fitter');
        Auth::login($user);

        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => $model->HeadDsgnId,
                    'loft_id' => $loft->LoftId
                ]
            ],
            'grip' => [
                'type' => $gripType->GripTypeId,
                'size' => $gripSize->GripSzId
            ]
        ];

        $price = $this->pricingService->calculateClubPrice($clubData);
        
        // Expected: 100 (model) + 25 (grip type) + 10 (grip size) + 15 (loft) = 150
        $this->assertEquals(150.00, $price);
    }

    /** @test */
    public function it_calculates_order_total_with_multiple_clubs()
    {
        // Create test models
        $model1 = HeadDsgn::factory()->create([
            'type' => 'b2b',
            'b2b_price' => 100.00
        ]);

        $model2 = HeadDsgn::factory()->create([
            'type' => 'b2b',
            'b2b_price' => 120.00
        ]);

        // Create a B2B user
        $user = User::factory()->create();
        $user->assignRole('fitter');
        Auth::login($user);

        $clubs = [
            [
                'category' => [
                    'model' => [
                        'model_id' => $model1->HeadDsgnId,
                        'quantity' => 2
                    ]
                ]
            ],
            [
                'category' => [
                    'model' => [
                        'model_id' => $model2->HeadDsgnId,
                        'quantity' => 1
                    ]
                ]
            ]
        ];

        $totalPrice = $this->pricingService->calculateOrderTotal($clubs);
        
        // Expected: (100 * 2) + (120 * 1) = 320
        $this->assertEquals(320.00, $totalPrice);
    }

    /** @test */
    public function it_returns_zero_for_missing_model()
    {
        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => 99999 // Non-existent model
                ]
            ]
        ];

        $price = $this->pricingService->calculateClubPrice($clubData);
        
        $this->assertEquals(0.00, $price);
    }

    /** @test */
    public function it_provides_pricing_breakdown()
    {
        $model = HeadDsgn::factory()->create([
            'type' => 'b2b',
            'b2b_price' => 100.00
        ]);

        $gripType = GripType::factory()->create(['price' => 25.00]);

        // Create a B2B user
        $user = User::factory()->create();
        $user->assignRole('fitter');
        Auth::login($user);

        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => $model->HeadDsgnId
                ]
            ],
            'grip' => [
                'type' => $gripType->GripTypeId
            ]
        ];

        $breakdown = $this->pricingService->getPricingBreakdown($clubData);
        
        $this->assertArrayHasKey('model_price', $breakdown);
        $this->assertArrayHasKey('grip_type_price', $breakdown);
        $this->assertArrayHasKey('total_price', $breakdown);
        $this->assertArrayHasKey('is_b2b', $breakdown);
        
        $this->assertEquals(100.00, $breakdown['model_price']);
        $this->assertEquals(25.00, $breakdown['grip_type_price']);
        $this->assertEquals(125.00, $breakdown['total_price']);
        $this->assertTrue($breakdown['is_b2b']);
    }
}
