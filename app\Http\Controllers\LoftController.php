<?php

namespace App\Http\Controllers;

use App\Models\Loft;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class LoftController extends Controller
{

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $loftAngles = Loft::latest()->get();

            return DataTables::of($loftAngles)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->LoftId . '">';
                })
                ->addIndexColumn()
                ->addColumn('price', function ($row) {
                    return $row->price ?? '0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                       model_id="' . $row->LoftId . '"
                                       model_LoftDsc="' . $row->LoftDsc . '"
                                       model_LoftPrice="' . $row->price . '"
                                       model_LoftDiff="' . $row->LoftDiff . '">Edit</a></li>
                                <li>
                                <form action="' . route('loft.destroy', $row->LoftId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['status','price','action','checkbox'])
                ->make(true);
        }

        return view('dashboard.Cruds.Lofts.index');
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'LoftDsc' => 'required',
        ], [
            'LoftDsc.required' => 'Loft name is required.',
            'LoftDsc.unique' => 'Loft name already exist.',
        ]);
        try {
            DB::beginTransaction();
            $LoftCd = strtoupper($request->LoftDsc[0]) . number_format(abs((float)$request->LoftDiff), 1, '.', '');
            Loft::create([
                'LoftCd' => $LoftCd ?? null,
                'LoftDsc' => $LoftDsc ?? null,
                'LoftDiff' => $LoftDiff ?? null,
                'price' => isset($request->LoftPrice)  ? $request->LoftPrice : 0
            ]);
            DB::commit();
            return redirect()->route('loft.index')->with(['title' => 'Done', 'message' => 'Loft created successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('loft.index')->with(['title' => 'Fail', 'message' => 'Unable to create Loft.', 'type' => 'error']);

        }
    }

    public function show($id)
    {
        //
    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request, $id)
    {
        $loft = Loft::where('LoftId', $request->model_id_update)->firstOrFail();

        $request->validate([
            'LoftDsc_update' => 'required',
        ], [
            'LoftDsc_update.required' => 'Loft name iss required.',
            'LoftDsc_update.unique' => 'Loft name already exists.',
        ]);

        try {
            DB::beginTransaction();
            $LoftCd = strtoupper($request->LoftDsc_update[0]) . number_format(abs((float)$request->LoftDiff_update), 1, '.', '');

            $loft->update([
                'LoftCd' => $LoftCd ?? null,
                'LoftDsc' => $request->LoftDsc_update ?? null,
                'LoftDiff' => $request->LoftDiff_update ?? null,
                'price' => isset($request->LoftPrice_update)  ? $request->LoftPrice_update : 0
            ]);

            DB::commit();

            return redirect()->route('loft.index')->with([
                'title' => 'Done',
                'message' => 'Loft updated successfully.',
                'type' => 'success',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('loft.index')->with([
                'title' => 'Fail',
                'message' => 'Update failed: ' . $e->getMessage(),
                'type' => 'error',
            ]);
        }
    }

    public function destroy($id)
    {
        try {
            Loft::where('LoftId', $id)->delete();
            return redirect()->route('loft.index')->with(['title' => 'Done', 'message' => 'Loft deleted successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            return redirect()->route('loft.index')->with(['title' => 'Fail', 'message' => 'Unable to delete Loft.', 'type' => 'error']);
        }

    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $gripSize = Loft::where('LoftId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            $gripSize->status = $request->status;
            $gripSize->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Loft status updated successfully.',
                'status' => $gripSize->status,
                'resultHtml' => ($gripSize->status == 1) ? '<span style="cursor:pointer;" class="success change-grip-size-status"  loft_id="'.$gripSize->LoftId.'" loft_status="0" >Active</span>' : '<span class="danger change-grip-size-status" loft_id="'.$gripSize->LoftId.'" loft_status="1"  style="cursor:pointer;">Inactive</span>'

            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update loft status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
