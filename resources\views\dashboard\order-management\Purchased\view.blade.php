@extends('theme.layout.master')
@push('css')
    <style>
        .club-detail-section {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .club-model-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2d8b57;
            margin-top: 10px;
            margin-bottom: 8px;
            text-decoration: underline;
        }

        .spec-detail p {
            font-size: 1rem;
            margin: 5px 0;
            color: #555;
        }

        .spec-detail strong {
            color: #333;
        }

        .spec-detail p:last-child {
            margin-bottom: 0;
        }

    </style>
    <style>
        /* Base Styles */
        .submitted_survey_sec {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .pipeline_stages_scroll {
            overflow-x: scroll;
            padding-bottom: 15px;
        }

        .pipeline_stages_scroll::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,.1);
            border-radius: 8px;
            background-color: #F5F5F5;
        }

        .pipeline_stages_scroll::-webkit-scrollbar {
            width: 10px;
            height: 8px;
        }

        .pipeline_stages_scroll::-webkit-scrollbar-thumb {
            border-radius: 8px;
            -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.1);
            background-color: #e0e0e0;
        }

        .pipeline_stages_div, .dragable_row_main_ {
            display: flex;
            overflow-x: auto;
            width: fit-content;
            flex-direction: row;
            flex-wrap: nowrap;
        }

        /* Stage Columns */
        .submitted_survey_sec .pipeline_stages__box {
            width: 200px;
            min-width: 200px;
            padding: 0 8px;
        }

        .submitted_survey_sec .inner_section_submitted_survey {
            padding: 5px 5px 10px 5px;
            border-radius: 8px;
            background: #3a994a45;
            position: relative;
        }
        .submitted_survey_sec .inner_section_submitted_survey:before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(92deg, #48B64C -14.76%, #205021 125.87%);
            border-radius: 0px 0px 8px 8px;
        }
        .submitted_survey_sec .survey_title h4 {
            font-size: 13px;
            margin: 0;
            font-family: 'Poppins-Regular';
            color: var(--black);
            font-weight: 600;
            text-align: center;
        }

        /* Opportunity Cards */
        .submitted_survey_sec .dragable_row_main_ .column {
            width: 200px;
            min-width: 200px;
            padding: 15px 0 0 0;
        }

        .submitted_survey_sec .dragable_row_main_ .column > .portlet {
            padding: 0 10px;
            width: 100%;
            border: 0;
        }

        .portlet {
            margin: 0 0 15px 0;
        }

        .inner_section_dragable {
            background: #fff;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .inner_section_dragable:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .card_header {
            margin-bottom: 4px;
        }

        .header_main_wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .userImg_name_wrapper {
            display: flex;
            align-items: center;
        }

        .user_image {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .user_name_date .user_name h4 a {
            color: var(--black);
            text-decoration: none;
            font-family: 'Poppins-SemiBold';
            padding: 0px 0 0 8px;
            font-size: 12px;
        }

        .action_btn button {
            background: transparent;
            border: none;
            color: #6c757d;
            font-size: 16px;
            padding: 0 4px;
        }

        .card_footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 4px;
            border-top: 1px solid #f0f0f0;
        }

        .card_footer .price label,
        .card_footer .quantity label {
            color: var(--black);
            font-weight: 600;
            font-size: 12px;
            font-family: 'Poppins-Medium';
        }

        .card_footer .price label span,
        .card_footer .quantity label span {
            color: var(--black);
            font-weight: 600;
            font-size: 12px;
            font-family: 'Poppins-Medium';
        }

        /* Dropdown Menu */
        .dropdown-menu {
            padding: 8px 0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }

        .dropdown-item {
            padding: 6px 16px;
            font-size: 14px;
            color: #333;
        }
        /* Drag and Drop Placeholder */
        .portlet-placeholder {
            border: 2px dashed #dee2e6;
            background: #f8f9fa;
            margin: 0 0 15px 0;
            height: 100px;
            border-radius: 8px;
        }
        .submitted_survey_sec .dragable_row_main_ .column .header_main_wrapper .action_btn ul.dropdown-menu.show a {
            padding: 0;
        }
    </style>
@endpush
@section('content')
    <section class="view-dashboard view_profile_management">
        <div class="container-fluid">
            <div class="row custom_row_gap">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="user_info">
                                    <div class="custom_justify">
                                        <h1><strong>ORDER NO :</strong> {{ $order->OrdNo ?? '' }}</h1>
                                        <div class="form-group">
                                            <select name="" id="" class="form-select">
                                                <option value="" selected disabled>Select Any Option</option>
                                                @foreach($orderStatus as $status)
                                                    <option value="{{$status->OrdStatId??''}}" @if(isset($order->orderStatus) && $status->OrdStatId == $order->orderStatus->OrdStatId) selected @endif>{{$status->OrdStatDsc??''}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <ul class="list-unstyled">
                                        <li>
                                            <i class="fa-solid fa-user"></i>{{ $order->customer->FirstNm ?? '' }} {{ $order->customer->LastNm ?? '' }}
                                        </li>
                                        @if(isset($order->customer->PhnNum))
                                            <li><i class="fa-solid fa-phone"></i>{{  $order->customer->PhnNum ?? ''  }}
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                                <div class="user_card_detail">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="custom_card">
                                                <img src="{{ asset('website') }}/assets/images/card-bar.svg">
                                                <h2>{{ $order->clubDetail->sum('Qty') ?? 1 }}</h2>
                                                <h5>Total Products</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="custom_card">
                                                <img src="{{ asset('website') }}/assets/images/card-bar.svg">
                                                <h2>${{ number_format($order->TtlAmt ?? 0, 2) }}</h2>
                                                <h5>Total Amount</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="fitter_view_tabs">
                                    <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="pills-general-tab" data-bs-toggle="pill" data-bs-target="#pills-general" type="button" role="tab" aria-controls="pills-general" aria-selected="true">
                                                <img class="img-fluid" src="{{ asset('website/assets/images/information.svg') }}"/>
                                                Customer Info
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-orders-tab" data-bs-toggle="pill" data-bs-target="#pills-orders" type="button" role="tab" aria-controls="pills-orders" aria-selected="false">
                                                <img class="img-fluid" src="{{ asset('website/assets/images/Orders.svg') }}"/>
                                                Fitter Info
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-payments-tab" data-bs-toggle="pill" data-bs-target="#pills-payments" type="button" role="tab" aria-controls="pills-payments" aria-selected="false">
                                                <img class="img-fluid" src="{{ asset('website/assets/images/user (2).png') }}"/>
                                                Order Info
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-status-tab" data-bs-toggle="pill" data-bs-target="#pills-status" type="button" role="tab" aria-controls="pills-status" aria-selected="false">
                                                <img class="img-fluid" src="{{ asset('website/assets/images/status-up.png') }}"/>
                                                Production Management
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <!-- Tabs Content -->
                        <div class="tab-content" id="pills-tabContent">
                            <!-- Customer Info Tab -->
                            <div class="tab-pane fade show active" id="pills-general" role="tabpanel"
                                 aria-labelledby="pills-general-tab" tabindex="0">
                                <div class="user_general_info">
                                    <h3>Customer Info</h3>
                                    <div class="general_detail">
                                        <div class="txt_field"><label>Name</label>
                                            <span>{{ $order->customer->FirstNm ?? '' }} {{ $order->customer->LastNm ?? '' }}</span>
                                        </div>
                                        <div class="txt_field"><label>E-Mail</label>
                                            <span>{{ $order->customer->EMailAddr ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Phone Number</label>
                                            <span>{{ $order->customer->PhnNum ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Street</label>
                                            <span>{{ $order->customer->Addr ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>City/Town</label>
                                            <span>{{ $order->customer->CityNm ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>State/Province/Region</label>
                                            <span>{{ $order->customer->StateCd ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Zip/Postal Code</label>
                                            <span>{{ $order->customer->PostalCd ?? 'N/A' }}</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- Filter Info -->
                            <div class="tab-pane fade" id="pills-orders" role="tabpanel"
                                 aria-labelledby="pills-orders-tab" tabindex="0">
                                <div class="user_general_info">
                                    <h3>Fitter Info</h3>
                                    <div class="general_detail">
                                        <div class="txt_field"><label>Name</label>
                                            <span>{{ $order->fitter->FtrNmFirst ?? '' }} {{ $order->fitter->FtrNmLast ?? '' }}</span>
                                        </div>
                                        <div class="txt_field"><label>E-Mail</label>
                                            <span>{{ $order->fitter->EMailAddr ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Phone Number</label>
                                            <span>{{ $order->fitter->CellPhone ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Street</label>
                                            <span>{{ $order->fitter->Addr ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>City/Town</label>
                                            <span>{{ $order->fitter->CityNm ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>State/Province/Region</label>
                                            <span>{{ $order->fitter->StateCd ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Zip/Postal Code</label>
                                            <span>{{ $order->fitter->PostalCd ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Account/Golf Course Name</label>
                                            <span>{{ $order->fitter->account->AcctNm ?? 'N/A' }}</span></div>
                                        <div class="txt_field"><label>Fitter Commission</label>
                                            <span>{{ $order->fitter->commission ?? 0 }}</span></div>
                                        <div class="txt_field"><label>BIO</label> <span>N/A</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- Pricing Tab -->
                            <div class="tab-pane fade" id="pills-payments" role="tabpanel"
                                 aria-labelledby="pills-payments-tab">
                                <div class="user_general_info">
                                    <h3>Order Info</h3>
                                    <div class="cart_sidebars">
                                        @foreach($order->clubDetail as $club)
                                            <div class="product_cart_box">
                                                <div class="cart_sidebar_img">
                                                    <img class="img-fluid" src="{{ asset('website/' . ($club->category->image ?? 'models/default.png')) }}"/>
                                                </div>
                                                <div class="cart_sidebar_info">
                                                    <h5>{{ $club->model->HeadDsgnCd ?? '' }}
                                                        - {{ $club->model->HeadDsgnDsc ?? '' }}</h5>
                                                    <h6>{{$club->EachAmt??''}}</h6>
                                                    <div class="color_type">
                                                        <p>Color:</p>
                                                        <span style="background-color: {{ $club->Color ?? '' }}; width: 25px; height: 25px; border: 1px solid grey"></span>
                                                    </div>
                                                    <div class="color_type">
                                                        <p>Quantity:</p>
                                                        <span>{{ $club->Qty ?? 0 }}</span>
                                                    </div>
                                                </div>
                                                <button
                                                    class="btn dark_green_btn d-flex align-self-start align-items-center"
                                                    data-bs-toggle="modal" data-bs-target="#orderDetailModal"
                                                    onclick="showOrderDetail({{ $club->ClbDtlId }})">
                                                    <i class="fas fa-eye" style="padding-right: 8px;"></i> View Detail
                                                </button>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <!-- Pricing Tab -->
                            <div class="tab-pane fade" id="pills-status" role="tabpanel"
                                 aria-labelledby="pills-status-tab">
                                {{--                                <div class="user_general_info">--}}
                                {{--                                    <h3>Status Management</h3>--}}
                                {{--                                    <div class="summarys">--}}
                                {{--                                        <div class="summary_box">--}}
                                {{--                                            <div class="detail_content">--}}
                                {{--                                                <h6>Order Summary</h6>--}}
                                {{--                                                <div class="detail_value sub_total">--}}
                                {{--                                                    <p>Sub Total</p>--}}
                                {{--                                                    <p>$319.98</p>--}}
                                {{--                                                </div>--}}
                                {{--                                                <div class="detail_value discount">--}}
                                {{--                                                    <p>Discount</p>--}}
                                {{--                                                    <p>10%</p>--}}
                                {{--                                                </div>--}}
                                {{--                                                <div class="detail_value shipping">--}}
                                {{--                                                    <p>Shipping</p>--}}
                                {{--                                                    <p class="free">Free</p>--}}
                                {{--                                                </div>--}}
                                {{--                                                <div class="detail_value coupon_applied">--}}
                                {{--                                                    <p>Coupon Applied</p>--}}
                                {{--                                                    <p>$0.00</p>--}}
                                {{--                                                </div>--}}
                                {{--                                            </div>--}}
                                {{--                                            <div class="total_content">--}}
                                {{--                                                <div class="total_value">--}}
                                {{--                                                    <p>TOTAL</p>--}}
                                {{--                                                    <p>${{$order->TltAmt??''}}</p>--}}
                                {{--                                                </div>--}}
                                {{--                                                <div class="total_value estimated">--}}
                                {{--                                                    <p>Estimated Delivery by</p>--}}
                                {{--                                                    <p>01 Feb, 2023</p>--}}
                                {{--                                                </div>--}}
                                {{--                                                <div class="view_more_btn light">--}}
                                {{--                                                    <a href="" class="btn btn-primary btn-sort">Shipped</a>--}}
                                {{--                                                </div>--}}
                                {{--                                            </div>--}}
                                {{--                                        </div>--}}
                                {{--                                    </div>--}}
                                {{--                                </div>--}}
                                <div class="submitted_survey_sec">
                                    <div class="container-fluid pipeline_stages_scroll">
                                        <div class="row pipeline_stages_div" id="pipeline_stages_div">
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>Cutting Station</h4></div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>Heading Station</h4></div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>Loft & Lie Station</h4></div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>SST Puring Station</h4></div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>Epoxy Station</h4></div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>Gripping Station</h4></div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>Final Check Station</h4></div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 pipeline_stages__box">
                                                <div class="inner_section_submitted_survey">
                                                    <div class="survey_title"><h4>Shipping Station</h4></div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row dragable_row_main_">
                                            <div class="column" id="1">
                                                <div class="portlet sotrting_index" data-start-index="1">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="123" data-contact-name="John Doe">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image">
                                                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>
                                                                        </div>
                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="1">John Doe</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="1">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="123" data-opportunity-id="1">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>100</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="column" id="2">
                                                <div class="portlet sotrting_index" data-start-index="2">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="124" data-contact-name="Jane Smith">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image">
                                                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>
                                                                        </div>                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="2">Jane Smith</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="2">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="124" data-opportunity-id="2">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/2" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>200</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="column" id="3">
                                                <div class="portlet sotrting_index" data-start-index="3">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="125" data-contact-name="Alice Johnson">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image">
                                                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>
                                                                        </div>                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="3">Alice Johnson</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton3" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton3">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="3">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="125" data-opportunity-id="3">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/3" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>300</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="column" id="4">
                                                <div class="portlet sotrting_index" data-start-index="4">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="126" data-contact-name="Bob Wilson">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image">
                                                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>
                                                                        </div>                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="4">Bob Wilson</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton4" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton4">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="4">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="126" data-opportunity-id="4">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/4" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>400</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="column" id="5">
                                                <div class="portlet sotrting_index" data-start-index="5">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="127" data-contact-name="Carol Brown">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image"><span>CB</span></div>
                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="5">Carol Brown</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton5" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton5">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="5">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="127" data-opportunity-id="5">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/5" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>500</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="portlet sotrting_index" data-start-index="6">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="128" data-contact-name="David Lee">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image">
                                                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>
                                                                        </div>                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="6">David Lee</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton6" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton6">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="6">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="128" data-opportunity-id="6">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/6" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>600</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="column" id="6"></div>

                                            <div class="column" id="7">
                                                <div class="portlet sotrting_index" data-start-index="7">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="127" data-contact-name="Carol Brown">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image"><span>CB</span></div>
                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="5">Carol Brown</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton5" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton5">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="5">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="127" data-opportunity-id="5">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/5" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>500</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="column" id="8">
                                                <div class="portlet sotrting_index" data-start-index="8">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="127" data-contact-name="Carol Brown">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image"><span>CB</span></div>
                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="5">Carol Brown</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton5" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton5">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="5">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="127" data-opportunity-id="5">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/5" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>500</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="portlet sotrting_index" data-start-index="9">
                                                    <div class="portlet grid-stack-item-content">
                                                        <div class="inner_section_dragable" data-status="1" data-contact="128" data-contact-name="David Lee">
                                                            <div class="portlet-header card_header">
                                                                <div class="header_main_wrapper">
                                                                    <div class="userImg_name_wrapper">
                                                                        <div class="user_image">
                                                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>
                                                                        </div>                                                                        <div class="user_name_date">
                                                                            <div class="user_name">
                                                                                <h4><a class="dropdown-item view_opportunity_detail" opportunity_id="6">David Lee</a></h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="action_btn">
                                                                        <div class="dropdown">
                                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton6" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton6">
                                                                                <li><a class="dropdown-item view_opportunity_detail" opportunity_id="6">View</a></li>
                                                                                <li><a class="dropdown-item edit_opportunity_detail" href="javascript:void(0)" data-contact-id="128" data-opportunity-id="6">Edit</a></li>
                                                                                <li>
                                                                                    <form method="POST" action="/opportunity/opportunity/6" accept-charset="UTF-8" style="display:inline">
                                                                                        <input type="hidden" name="_method" value="DELETE">
                                                                                        <input type="hidden" name="_token" value="STATIC_CSRF_TOKEN">
                                                                                        <a class="dropdown-item" onclick="if(confirm('Confirm delete?')){event.preventDefault();this.parentNode.submit();}">Delete</a>
                                                                                    </form>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card_footer">
                                                                <div class="price"><label>$<span>600</span></label></div>
                                                                <div class="quantity"><label>Qty: <span>1</span></label></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Order Detail Modal -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1" aria-labelledby="orderDetailModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderDetailModalLabel">Order Detail</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-xmark"></i></button>

                </div>
                <div class="modal-body">
                    <div id="orderDetailsContent">
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <script>
        function showOrderDetail(clubId) {
            $('#orderDetailsContent').html('<p>Loading...</p>');

            $.ajax({
                url: "{{ route('order.club.detail', ['clubId' => '__clubId__']) }}".replace('__clubId__', clubId),
                method: 'GET',
                success: function (data) {
                    if (data.html) {
                        $('#orderDetailsContent').html(data.html);
                    } else {
                        alert('No details available for this club.');
                    }
                },
                error: function (xhr, status, error) {
                    alert('Error loading order details.');
                }
            });
        }

    </script>
    <script>
        $(document).ready(function(){
            $(".column").sortable({
                connectWith: ".column",
                handle: ".portlet-header",
                cancel: ".portlet-toggle",
                placeholder: "portlet-placeholder ui-corner-all",
                tolerance: "pointer",
                cursorAt: { top: 25, left: 25 },
                forcePlaceholderSize: true,
                refreshPositions: true,
                revert: 450,
                start: function(e, t) {
                    t.item.data("start-index", t.item.index());
                    t.item.data("start-column", t.item.closest(".column").attr("id"));
                },
                stop: function(e, t) {
                    var n = t.item.data("start-index"),
                        r = t.item.index(),
                        a = t.item.data("start-column"),
                        i = t.item.closest(".column").attr("id"),
                        o = n - r,
                        s = t.item.find(".view_opportunity_detail").attr("opportunity_id");

                    console.log(`Opportunity ${s} moved ${o > 0 ? "up" : "down"} by ${Math.abs(o)} position(s) from index ${n} to ${r} in column ${i}`);

                    var d = $(".column").find(".portlet");
                    printIndexes(d);

                    var c = {
                        _token: "STATIC_CSRF_TOKEN",
                        stageId: i,
                        opportunityId: s,
                        positions: getOpportunityPositions(d)
                    };

                    updateOpportunityPositions(c);
                }
            });

            $(".portlet")
                .addClass("ui-widget ui-widget-content ui-helper-clearfix ui-corner-all")
                .find(".portlet-header")
                .addClass("ui-widget-header ui-corner-all")
                .prepend("<span class='ui-icon ui-icon-minusthick portlet-toggle'></span>");

            $(".portlet-toggle").on("click", function() {
                var e = $(this);
                e.toggleClass("ui-icon-minusthick ui-icon-plusthick");
                e.closest(".portlet").find(".portlet-content").toggle();
            });
        });

        function printIndexes(e) {
            console.log("Opportunity Indexes:");
            var t = [];

            e.each(function() {
                var e = $(this).find(".view_opportunity_detail").attr("opportunity_id"),
                    n = $(this).closest(".column").attr("id");

                if (!t.includes(e)) {
                    var r = $(this).index();
                    console.log(`Opportunity ${e} - Column: ${n}, Index: ${r}`);
                    t.push(e);
                }
            });
        }

        function getOpportunityPositions(e) {
            var t = [],
                n = {};

            return e.each(function() {
                var e = $(this).find(".view_opportunity_detail").attr("opportunity_id"),
                    r = $(this).closest(".column").attr("id"),
                    a = $(this).index();

                if (!n[e]) {
                    t.push({
                        opportunityId: e,
                        columnId: r,
                        position: a
                    });
                    n[e] = true;
                }
            }), t;
        }

        function updateOpportunityPositions(e) {
            console.log("Sending data:", e);
        }
    </script>@endpush

