<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShfFlx extends Model
{
    use HasFactory;
    use softDeletes;
    protected $primaryKey = 'ShfFlxCd';
    public $incrementing = false;
    protected $keyType ='string';
    protected $table = 'shfflx';
    protected $guarded = [];
    protected $appends = ['StatusHtml'];

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-flex-status"  model_id="'.$this->ShfFlxCd.'" model_status="0" >Active</span>' : '<span class="danger change-model-status" model_id="'.$this->ShfFlxCd.'" model_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
