<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lie extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'lie';
    protected $guarded = [];
    protected $primaryKey = 'LieId';

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-lie-status"  lie_id="'.$this->LieId.'" lie_status="0" >Active</span>' : '<span class="danger change-grip-size-status" lie_id="'.$this->LieId.'" lie_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
