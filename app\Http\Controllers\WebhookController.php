<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;


class WebhookController extends Controller
{
    public function handleCreateCustomer(Request $request)
    {
        // Log or inspect the incoming webhook payload
        Log::info('Stax Webhook (create_customer) Received:', $request->all());

        // Respond with 200 OK (required for webhook to be considered successful)
        return response()->json(['message' => 'Webhook received.'], 200);
    }
}
