<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShfLen extends Model
{
    use HasFactory;
    use softDeletes;
    protected $primaryKey = 'ShfLenId';
    protected $table = 'shflen';
    protected $guarded = [];

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-shflen-status"  shflen_id="'.$this->ShfLenId.'" shflen_status="0" >Active</span>' : '<span class="danger change-model-status" shflen_id="'.$this->ShfLenId.'" shflen_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
