<?php

namespace App\Http\Controllers;

use App\Models\ClbDtl;
use App\Models\ClbType;
use App\Models\Ord;
use App\Models\OrdStat;
use App\Models\Cntry;
use App\Models\Lie;
use App\Models\Loft;
use App\Models\FaceAngle;
use App\Models\Cust;
use App\Models\GripType;
use App\Models\GripSz;
use App\Models\ShfLen;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
//    public function index(Request $request, $status = null)
//    {
//        if ($request->ajax()) {
//            $orderQuery = Ord::orderByDesc('OrdId');
//            if ($status != 'all') {
//                $orderQuery->where('status', $status);
//            }
//
//            $searchValue = $request->input('search.value');
//            if (!empty($searchValue)) {
//                $orderQuery->where(function ($query) use ($searchValue) {
//                    $query->where('OrdNo', 'like', "%{$searchValue}%")
//                        ->orWhereHas('fitter', function ($q) use ($searchValue) {
//                            $q->where('FtrNmFirst', 'like', "%{$searchValue}%")
//                                ->orWhere('FtrNmLast', 'like', "%{$searchValue}%");
//                        })
//                        ->orWhereHas('customer', function ($q) use ($searchValue) {
//                            $q->where('FirstNm', 'like', "%{$searchValue}%")
//                                ->orWhere('LastNm', 'like', "%{$searchValue}%");
//                        });
//                });
//            }
//
//            $orderQuery = $orderQuery->with(['clubDetail', 'fitter', 'customer'])->get();
//
//            return DataTables::of($orderQuery)
//                ->addColumn('fitter', function ($row) {
//                    return $row->fitter ? $row->fitter->FtrNmFirst . ' ' . $row->fitter->FtrNmLast : 'N/A';
//                })
//                ->addColumn('customer', function ($row) {
//                    return $row->customer ? $row->customer->FirstNm . ' ' . $row->customer->LastNm : 'N/A';
//                })
//                ->addColumn('total_qty', function ($row) {
//                    return $row->clubDetail ? $row->clubDetail->sum('Qty') : 'N/A';
//                })
//                ->addColumn('total_price', function ($row) {
//                    return $row->TtlAmt??'0';
//                })
//                ->addColumn('order_type', function ($row) {
//                    return $row->orderType->OrdTypeDsc??'-';
//                })
//                ->addColumn('bill_type', function ($row) {
//                    return $row->billType->BillCdDsc??'-';
//                })
//                ->addColumn('ship_via', function ($row) {
//                    return $row->shipViaType->ShipViaDsc??'-';
//                })
//                ->addColumn('current_status', function ($row) {
//                    return $row->orderStatus->OrdStatDsc??'-';
//                })
//                ->addColumn('action', function ($row) {
//                    return '
//                <div class="dropdown">
//                    <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
//                        <img class="ellipsis_img" src="' . asset('website/assets/images/ellipsis.svg') . '">
//                    </button>
//                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
//                        <li><a class="dropdown-item" href="' . route('orders.show', $row->OrdId) . '">View</a></li>
//                    </ul>
//                </div>';
//                })
//                ->rawColumns(['action'])
//                ->make(true);
//        }
//
//        if ($status == 'paid') {
//            return view('dashboard.order-management.Purchased.index', compact('status'));
//
//        }
//
//        if ($status == 'completed') {
//            return view('dashboard.order-management.Completed.index', compact('status'));
//
//        }
//
//        return view('dashboard.order-management.Quotation.index', compact('status'));
//    }

    public function index(Request $request, $status = null)
    {
        if ($request->ajax()) {
            $orderQuery = Ord::query()
                ->with([
                    'clubDetail:ClbDtlId,OrdId,Qty',
                    'fitter:FtrId,FtrNmFirst,FtrNmLast',
                    'customer:CustId,FirstNm,LastNm',
                    'orderType:OrdTypeCd,OrdTypeDsc',
                    'billType:BillCd,BillCdDsc',
                    'shipViaType:ShipViaId,ShipViaDsc',
                    'orderStatus:OrdStatId,OrdStatDsc'
                ])
                ->orderByDesc('OrdId');
            if ($status !== 'all') {
                $orderQuery->where('status', $status);
            }
            $searchValue = $request->input('search.value');
            if (!empty($searchValue)) {
                $orderQuery->where(function ($query) use ($searchValue) {
                    $query->where('OrdNo', 'like', "%{$searchValue}%")
                        ->orWhereHas('fitter', function ($q) use ($searchValue) {
                            $q->where('FtrNmFirst', 'like', "%{$searchValue}%")
                                ->orWhere('FtrNmLast', 'like', "%{$searchValue}%");
                        })
                        ->orWhereHas('customer', function ($q) use ($searchValue) {
                            $q->where('FirstNm', 'like', "%{$searchValue}%")
                                ->orWhere('LastNm', 'like', "%{$searchValue}%");
                        });
                });
            }

            return DataTables::eloquent($orderQuery)
                ->addColumn('fitter', function ($row) {
                    return optional($row->fitter)->FtrNmFirst . ' ' . optional($row->fitter)->FtrNmLast ?? 'N/A';
                })
                ->addColumn('customer', function ($row) {
                    return optional($row->customer)->FirstNm . ' ' . optional($row->customer)->LastNm ?? 'N/A';
                })
                ->addColumn('total_qty', function ($row) {
                    return $row->clubDetail->sum('Qty');
                })
                ->addColumn('total_price', fn($row) => $row->TtlAmt ?? '0')
                ->addColumn('order_type', fn($row) => optional($row->orderType)->OrdTypeDsc ?? '-')
                ->addColumn('bill_type', fn($row) => optional($row->billType)->BillCdDsc ?? '-')
                ->addColumn('ship_via', fn($row) => optional($row->shipViaType)->ShipViaDsc ?? '-')
                ->addColumn('current_status', fn($row) => optional($row->orderStatus)->OrdStatDsc ?? '-')
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website/assets/images/ellipsis.svg') . '">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item" href="' . route('orders.show', $row->OrdId) . '">View</a></li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        $view = match($status) {
            'paid' => 'dashboard.order-management.Purchased.index',
            'completed' => 'dashboard.order-management.Completed.index',
            default => 'dashboard.order-management.Quotation.index',
        };

        return view($view, compact('status'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $countries = Cntry::all();
        $customers = Cust::all();
        $categories = ClbType::all();
        $lieAngles = Lie::all();
        $faceAngles = FaceAngle::all();
        $lofts = Loft::all();
        $shfLens = ShfLen::all();
        $gripSize = GripSz::all();
        $gripTypes = GripType::all();
        return view('dashboard.order-management.Quotation.create', compact('countries', 'customers', 'categories','lieAngles','faceAngles','lofts','shfLens','gripSize','gripTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $order = Ord::where('OrdId', $id)->with(['clubDetail.category', 'clubDetail.model', 'clubDetail.shaft', 'fitter.account', 'customer'])->first();
        $orderStatus = OrdStat::orderBy('seq')->get();
        return view('dashboard.order-management.Purchased.view', compact('order','orderStatus'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }


    public function getClubDetail($clubId)
    {
        $club = ClbDtl::find($clubId);
        if (!$club) {
            return response()->json(['error' => 'Club not found'], 404);
        }
        $html = view('dashboard.order-management.order_detail_modal', compact('club'))->render();
        return response()->json(['html' => $html]);
    }

}
