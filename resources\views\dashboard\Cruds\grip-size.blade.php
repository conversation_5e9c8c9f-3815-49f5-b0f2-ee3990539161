@extends('theme.layout.master')
@push('css')
@endpush
@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>CRUDs  > Grip Sizes</h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_action_fields">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                                <!-- Filter Dropdown -->
                                <div class="dropdown-btn">
                                    <button type="button" class="btn dropdown-toggle light_green_btn" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                    <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                        <div class="dropdown_top">
                                            <h6 class="">Filter</h6>
                                            <button type="button" class="btn_close" data-bs-dismiss="dropdown" aria-label="Close">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        @include('theme.layout.partials.status_category_form_filter',['status'=>true])
                                    </div>
                                </div>
                                <!-- Create Button -->
                                <a href="{{ url('grip-sizes/create') }}" class="btn dark_green_btn"><i class="fa-solid fa-square-plus"></i>Create</a>
                            </div>
                            </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success">  <i class="fas fa-check-circle"></i> </button>
                                <button id="deactivateSelected" class="btn btn-danger">     <i class="fas fa-ban"></i> </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="grip-size-datatable table table-row-bordered gy-5" id="GripSizeTable">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Short Size</th>
                                    <th>Size</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
{{--                                @foreach($gripSizes as $gripSize)--}}
{{--                                    <tr>--}}
{{--                                        <td>{{$loop->iteration}}</td>--}}
{{--                                        <td>{{$gripSize->GripSzCd}}</td>--}}
{{--                                        <td>{{$gripSize->GripSzDsc}}</td>--}}
{{--                                        <td>{!! $gripSize->statusHtml !!}</td>--}}
{{--                                       <td>--}}
{{--                                           <div class="dropdown">--}}
{{--                                               <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">--}}
{{--                                                   <img class="ellipsis_img" src="{{ asset('website') }}/assets/images/ellipsis.svg">--}}
{{--                                               </button>--}}
{{--                                               <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">--}}
{{--                                                   <li>--}}
{{--                                                       <a class="dropdown-item edit_grip_size_button" href="javascript:void(0)" data-id="{{ $gripSize->GripSzId }}" data-name="{{ $gripSize->GripSzDsc }}" data-code="{{ $gripSize->GripSzCd }}" data-status="{{ $gripSize->status }}">Edit</a>--}}
{{--                                                   </li>--}}
{{--                                                   <li>--}}
{{--                                                       {!! Form::open(['method' => 'DELETE', 'route' => ['grip-sizes.destroy', $gripSize->GripSzId], 'class' => 'delete-form']) !!}--}}
{{--                                                       <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>--}}
{{--                                                       {!! Form::close() !!}--}}
{{--                                                   </li>--}}
{{--                                               </ul>--}}
{{--                                           </div>--}}
{{--                                    </tr>--}}
{{--                                @endforeach--}}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal fade custom_modal" id="update_grip_size" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Edit Grip Size</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="grip_size_form_update" action="{{ route('grip-sizes.update', 0) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="grip_sz_id_update" id="grip_sz_id_update">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Grip Size</label>
                                    <input type="text" class="form-control" id="grip_size_dsc_update" name="grip_size_dsc_update" placeholder="Type Here" value="{{ old('grip_size_dsc') }}">
                                    <div class="text-danger" id="grip_sz_dsc_error"></div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Grip Price</label>
                                    <input type="text" class="form-control" id="grip_size_price_update" name="grip_size_price_update" placeholder="Type Here" value="{{ old('grip_size_price') }}" oninput="formatPrice(this)">
                                    <div class="text-danger" id="grip_sz_price_error"></div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Status</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="" selected disabled>Select Status</option>
                                        <option value="1" class="active_status" >Active</option>
                                        <option value="0" class="inactive_status">Inactive</option>
                                    </select>
                                    <div class="text-danger" id="status_error"></div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Update</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')

    <script>

        $('#activateSelected, #deactivateSelected').on('click', function () {
            let status = $(this).attr('id') === 'activateSelected' ? 1 : 0;
            let selectedIds = [];

            $('.category-checkbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
            console.log(selectedIds , status);

            if (selectedIds.length > 0) {
                $.ajax({
                    url: '{{ route('toggle.status') }}',
                    method: 'POST',
                    data: {
                        ids: selectedIds,
                        status: status,
                        primary_key: 'GripSzId',
                        model: 'GripSz',
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Status Updated!',
                                text: 'The selected categories have been successfully updated.',
                                confirmButtonText: 'OK'
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'An error occurred while updating the status. Please try again later.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Nothing Selected',
                    text: 'Please select at least one category to activate/deactivate.',
                    confirmButtonText: 'OK'
                });
            }
        });

        $(document).ready(function () {
            // Create custom page length dropdown with 10, 25, 50, and All options
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';

            // Append the dropdown to the side fields container
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);

            var table = $('.grip-size-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('grip-sizes.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.status = $('#status').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'checkbox', name: 'checkbox' },
                    { data: null, name: null, defaultContent: '' },
                    { data: 'short_size', name: 'short_size' },
                    { data: 'size', name: 'size' },
                    { data: 'price', name: 'price' },
                    { data: 'status', name: 'status' },
                    { data: 'action', name: 'action', orderable: false, searchable: false }
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                initComplete: function (settings, json) {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Change the page length based on dropdown selection
            document.querySelector('#dt-length-0').addEventListener('change', function () {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

            $(".filter-form").submit(function (e) {
                e.preventDefault();
                table.draw();
            });

            // Select All functionality
            $('.select_all_checkboxes').on('click', function () {
                var checkboxes = $('.category-checkbox');
                var isChecked = $(this).prop('checked'); // Get the state of select_all_checkboxes
                checkboxes.prop('checked', isChecked); // Set all category-checkbox to the same state
            });

            // Update select_all_checkboxes when any category-checkbox is changed
            $(document).on('change', '.category-checkbox', function () {
                var checkboxes = $('.category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', allChecked); // Update select_all_checkboxes
            });
        });

    </script>

    <script>
        $(document).on('click', '.edit_grip_size_button', function () {
            var gripSzId = $(this).attr('data-id');
            var gripSzDsc = $(this).attr('data-name');
            var GripSzCd = $(this).attr('data-code');
            var GripSzStatus = $(this).attr('data-status');
            var price = $(this).attr('data-price');

            $('#grip_sz_id_update').val(gripSzId);
            $('#grip_size_dsc_update').val(gripSzDsc);
            $('#grip_sz_cd').val(GripSzCd);
            $('#grip_size_price_update').val(price);
            if(GripSzStatus == '1'){
                $('#status ').find('.active_status').attr('selected',true);  // Set status dropdown value and trigger change event if needed
            }else{
                $('#status ').find('.inactive_status').attr('selected',true);  // Set status dropdown value and trigger change event if needed
            }

            // Show the modal
            $('#update_grip_size').modal('show');
        });

        $(document).ready(function (){
            $('#grip_size_form_update').submit(function(e) {});
        });

        $('#grip_size_form_update').validate({
            rules: {
                grip_size_dsc_update: {
                    required: true
                },
                grip_size_price_update: {
                    required: true
                }
            },
            messages: {
                grip_size_dsc_update: "Please enter a grip size",
                grip_size_price_update: "Please enter a grip price"
            },
            submitHandler: function (form) {
                form.submit();
            }
        });

        $(document).on('click', '.change-grip-size-status', function () {
            var gripSizeId = $(this).attr('grip_size_id');
            var currentStatus = $(this).attr('grip_size_status');
            var currentElement = $(this);

            // Confirm the action
            Swal.fire({
                title: 'Are you sure?',
                text: 'You are about to change the status.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var url = "{{ url('grip-sizes') }}/" + gripSizeId + "/status";
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: currentStatus
                        },
                        success: function (response) {
                            if (response.success) {
                                currentElement.html(response.resultHtml);
                                currentElement.removeClass().addClass(response.resultHtml);
                                swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 4000,
                                });
                            } else {
                                swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                    timer: 4000,
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while changing the status.',
                                timer: 4000,
                            });
                        }
                    });
                }
            });
        });
    </script>
@endpush
