<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class Cntry extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'cntry';
    protected $guarded = [];
    protected $primaryKey = 'CntryCd';
    public $incrementing = false;
    protected $keyType ='string';

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-cntry-status"  cntry_id="'.$this->CntryCd.'" cntry_status="0" >Active</span>' : '<span class="danger change-cntry-status" cntry_id="'.$this->CntryCd.'" cntry_status="1"  style="cursor:pointer;">Inactive</span>';
    }


/*span.success
span.danger
span.yellow
text_success
text_danger
text_yellow*/

}
