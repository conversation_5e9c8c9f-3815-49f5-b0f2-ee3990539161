{{--@extends('theme.layout.master')--}}
{{--@push('css')--}}
{{--@endpush--}}
{{--@section('content')--}}
{{--@section('breadcrumb')--}}
{{--<div class="container-fluid">--}}
{{--    <div class="row">--}}
{{--        <div class="col-md-12">--}}
{{--            <h1 class="page-header">{{ config('app.name') }}</h1>--}}
{{--            <ol class="breadcrumb">--}}
{{--                <li><a href="{{ url('home') }}">Home</a></li>--}}
{{--                <li class="active">hossels</li>--}}
{{--            </ol>--}}
{{--        </div>--}}
{{--    </div>--}}
{{--</div>--}}
{{--@endsection--}}
{{--<div class="container-fluid">--}}
{{--    <div class="panel panel-default">--}}
{{--        <div class="panel-heading">--}}
{{--            <div class="row">--}}
{{--                <div class="col-md-6">--}}
{{--                    <input type="text" class="form-control" placeholder="Search Customers">--}}
{{--                </div>--}}
{{--                <div class="col-md-6 text-right">--}}
{{--                    @can('hossels-create')--}}
{{--                    <a class="btn btn-primary" href="{{ route('hossels.create') }}">Add</a>--}}
{{--                    @endcan--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--        <div class="panel-body">--}}
{{--            <table class="table table-bordered table-hover">--}}
{{--                <thead>--}}
{{--                    <tr>--}}
{{--                        <th><input type="checkbox"></th>--}}
{{--                        				<th>category_id</th>--}}
{{--				<th>name</th>--}}
{{--				<th>status</th>--}}

{{--                        <th class="text-right">Actions</th>--}}
{{--                    </tr>--}}
{{--                </thead>--}}
{{--                <tbody>--}}
{{--                    @foreach($hossels as $hossel)--}}
{{--                    <tr>--}}
{{--                        <td><input type="checkbox" value="{{$hossel->id}}"></td>--}}
{{--                        					<td>{{ $hossel->category_id }}</td>--}}
{{--					<td>{{ $hossel->name }}</td>--}}
{{--					<td>{{ $hossel->status }}</td>--}}

{{--                        <td class="text-right">--}}
{{--                            <div class="btn-group">--}}
{{--                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>--}}
{{--                                <ul class="dropdown-menu pull-right">--}}
{{--                                    @can('hossels-list')--}}
{{--                                    <li><a href="{{ route('hossels.show', [$hossel->id]) }}">View</a></li>--}}
{{--                                    @endcan--}}
{{--                                    @can('hossels-edit')--}}
{{--                                    <li><a href="{{ route('hossels.edit', [$hossel->id]) }}">Edit</a></li>--}}
{{--                                    @endcan--}}
{{--                                    @can('hossels-delete')--}}
{{--                                    <li>--}}
{{--                                        {!! Form::open(['method' => 'DELETE', 'route' => ['hossels.destroy', $hossel->id], 'class' => 'delete-form']) !!}--}}
{{--                                        <a href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>--}}
{{--                                        {!! Form::close() !!}--}}
{{--                                    </li>--}}
{{--                                    @endcan--}}
{{--                                </ul>--}}
{{--                            </div>--}}
{{--                        </td>--}}
{{--                    </tr>--}}
{{--                    @endforeach--}}
{{--                </tbody>--}}
{{--            </table>--}}
{{--        </div>--}}
{{--    </div>--}}
{{--</div>--}}
{{--@stop--}}
{{--@push('js')--}}
{{--@endpush--}}

@extends('theme.layout.master')
@push('css')
@endpush
@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Cruds > Hossel</h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                                <!-- Filter Dropdown -->
                                <!-- Create Button -->
                                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#create_hossel" class="btn dark_green_btn"><i class="fa-solid fa-square-plus"></i>Create</a>
                            </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success">  <i class="fas fa-check-circle"></i> </button>
                                <button id="deactivateSelected" class="btn btn-danger">     <i class="fas fa-ban"></i> </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="hossel-datatable table table-row-bordered gy-5 custom_sizing" id="hosselTable">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Create Hossel Modal -->
    <div class="modal fade custom_modal crud_modal" id="create_hossel" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Create Hossel</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form" action="{{ url('hossels') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group select_two">
                                    <label>Select Category *</label>
                                    <select class="form-control category_id" name="category_id" id="category_id">
                                        <option value="" selected disabled>Select Category</option>
                                        @foreach($categories->where('status',1) as $category)
                                            <option
                                                value="{{$category->ClbTypeCd}}" {{ old('category_id') == $category->ClbTypeCd ? 'selected' : '' }} >{{$category->ClbTypeDsc}}</option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Name *</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="name" id="name" required>
                                    @error('name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Price</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="price" id="price" oninput="formatPrice(this)">
                                    @error('price')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Create</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade custom_modal" id="update_hossel" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Edit Hossel</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form_update" action="{{ route('hossels.update', 0) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="hossel_id_update" id="hossel_id_update">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group select_two">
                                    <label>Category</label>
                                    <select class="form-control" name="category_id_update" id="category_id_update">
                                        <option value="" disabled>Select Category</option>
                                        @foreach($categories->where('status',1) as $category)
                                            <option value="{{$category->ClbTypeCd}}" {{ old('category_id_update') == $category->ClbTypeCd ? 'selected' : '' }} >
                                                {{$category->ClbTypeDsc}}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id_update')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Name *</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="name_update" id="name_update" required>
                                    @error('name')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Price</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="price_update" id="price_update" oninput="formatPrice(this)">
                                    @error('price_update')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Update</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('js')

    <script>

        $('#activateSelected, #deactivateSelected').on('click', function () {
            let status = $(this).attr('id') === 'activateSelected' ? 1 : 0;
            let selectedIds = [];

            $('.category-checkbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
            console.log(selectedIds , status);

            if (selectedIds.length > 0) {
                $.ajax({
                    url: '{{ route('toggle.status') }}',
                    method: 'POST',
                    data: {
                        ids: selectedIds,
                        status: status,
                        primary_key: 'id',
                        model: 'Hossel',
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Status Updated!',
                                text: 'The selected categories have been successfully updated.',
                                confirmButtonText: 'OK'
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'An error occurred while updating the status. Please try again later.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Nothing Selected',
                    text: 'Please select at least one category to activate/deactivate.',
                    confirmButtonText: 'OK'
                });
            }
        });

        $(document).on('click', '.change-hossel-status', function () {
            var modelId = $(this).attr('hossel_id');
            var status = $(this).attr('hossel_status');
            var current_parent = $(this).parent();
            Swal.fire({
                title: 'Are you sure?',
                text: 'You are about to change the status.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var url = "{{ url('hossels') }}/" + modelId + "/status";
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: status
                        },
                        success: function (response) {
                            if (response.success) {
                                current_parent.html(response.resultHtml);
                                swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 4000,
                                });
                            } else {
                                swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                    timer: 4000,

                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while changing the status.',
                                timer: 4000,
                            });
                        }
                    });
                }
            });
        });

        $(document).on('click', '.edit_hossel_button', function () {
            const hosselId = $(this).attr('hossel_id');
            const hosselName = $(this).attr('hossel_name');
            const hosselCategory = $(this).attr('hossel_category_id');
            const hosselPrice = $(this).attr('hossel_price');
            $('#hossel_id_update').val(hosselId);
            $('#name_update').val(hosselName);
            $('#price_update').val(hosselPrice);
            $('#category_id_update').val(hosselCategory).trigger('change');
            $('#category_id_update').select2({dropdownParent: $('#update_hossel')});

            $('#update_hossel').modal('show');
        });

        $(document).on("click", ".close-btn", function () {
            var parentDiv = $(this).parent();
            parentDiv.remove();

            if ($(".code_value span").length === 0) {
                $(".code_value").hide();
            }

        });


        $(document).ready(function() {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>

    <script>
        $(document).ready(function () {
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';
            // Append the dropdown to the side fields container
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);

            // Variable to track Select All state
            var selectAllState = false;

            var table = $('.hossel-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('hossels.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.parent = $('#parent').val();
                        // Pass selectAllState to the server if needed for custom logic
                        d.selectAll = selectAllState;
                    },
                    dataSrc: 'data'
                },
                columns: [
                    {
                        data: 'checkbox',
                        name: 'checkbox',
                        render: function (data, type, row) {
                            // Ensure checkboxes reflect selectAllState on render
                            var checked = selectAllState ? 'checked' : '';
                            return '<input type="checkbox" class="category-checkbox" ' + checked + '>';
                        }
                    },
                    { data: null, name: null, defaultContent: '' },
                    { data: 'name', name: 'name' },
                    { data: 'category_id', name: 'category_id' },
                    { data: 'price', name: 'price' },
                    { data: 'status', name: 'status' },
                    { data: 'action', name: 'action', orderable: false, searchable: false }
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                initComplete: function (settings, json) {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Select All functionality
            $('.select_all_checkboxes').on('click', function () {
                selectAllState = $(this).prop('checked'); // Update selectAllState
                // Update checkboxes on the current page
                $('.category-checkbox').prop('checked', selectAllState);
                // Trigger table redraw to apply state to other pages
                table.ajax.reload(null, false); // Reload without resetting pagination
            });

            // Update select_all_checkboxes when any category-checkbox is changed
            $(document).on('change', '.category-checkbox', function () {
                var checkboxes = $('.category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;

                // If any checkbox is unchecked, disable selectAllState
                if (!$(this).prop('checked')) {
                    selectAllState = false;
                    $('.select_all_checkboxes').prop('checked', false);
                } else if (allChecked) {
                    // Only enable selectAllState if all checkboxes on current page are checked
                    selectAllState = true;
                    $('.select_all_checkboxes').prop('checked', true);
                }
            });

            // Ensure select_all_checkboxes state is updated after table redraw
            table.on('draw', function () {
                // Update checkboxes on current page based on selectAllState
                $('.category-checkbox').prop('checked', selectAllState);
                // Update select_all_checkboxes state
                var checkboxes = $('.category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', selectAllState && allChecked);
            });

            // Change the page length based on dropdown selection
            document.querySelector('#dt-length-0').addEventListener('change', function () {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });
        });
        $(document).ready(function () {
            $('.category_id').select2({
                dropdownParent: $('#create_hossel')
            });
        });
    </script>
@endpush
