<?php

namespace App\Http\Controllers;

use App\Models\Lie;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class LieAngleController extends Controller
{

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $lieAngles = Lie::latest()->get();

            return DataTables::of($lieAngles)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->LieId . '">';
                })
                ->addIndexColumn()
                ->addColumn('price', function ($row) {
                    return $row->price ?? '0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                       model_id="' . $row->LieId . '"
                                       model_LieDsc="' . $row->LieDsc . '"
                                       model_LiePrice="' . $row->price . '"
                                       model_LieDiff="' . $row->LieDiff . '">Edit</a></li>
                                <li>
                                <form action="' . route('lie-angles.destroy', $row->LieId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['status','price', 'action','checkbox'])
                ->make(true);
        }
        return view('dashboard.Cruds.Lie-Angles.index');
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'LieDsc' => 'required',
        ], [
            'LieDsc.required' => 'Lie angle name is required.',
            'LieDsc.unique' => 'Lie angle name already exist.',
        ]);

        try {
            DB::beginTransaction();
            $LieCd = strtoupper($request->LieDsc[0]) . number_format(abs((float)$request->LieDiff), 1, '.', '');

            Lie::create([
                'LieCd' => $LieCd ?? null,
                'LieDsc' => $LieDsc ?? null,
                'LieDiff' => $LieDiff ?? null,
                'price' =>  isset($request->LiePrice)  ? $request->LiePrice : 0,
            ]);

            DB::commit();

            return redirect(url('lie-angles'))->with(['title' => 'Done', 'message' => 'Lie angle created successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            DB::rollback();
            return redirect(url('lie-angles'))->with(['title' => 'Fail', 'message' => 'Unable to create Lie angle.', 'type' => 'error']);

        }

    }

    public function show($id)
    {
        //
    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request, $id)
    {
        $lie = Lie::where('LieId', $request->model_id_update)->firstOrFail();

        $request->validate([
            'LieDsc_update' => 'required',
        ], [
            'LieDsc_update.required' => 'Lie angle name is required.',
            'LieDsc_update.unique' => 'Lie angle name already exists.',
        ]);

        try {
            DB::beginTransaction();
            $LieCd = strtoupper($request->LieDsc_update[0]) . number_format(abs((float)$request->LieDiff_update), 1, '.', '');

            $lie->update([
                'LieCd' => $LieCd ?? null,
                'LieDsc' => $request->LieDsc_update ?? null,
                'LieDiff' => $request->LieDiff_update ?? null,
                'price' => isset($request->LiePrice_update)  ? $request->LiePrice_update : 0,
            ]);

            DB::commit();

            return redirect(url('lie-angles'))->with([
                'title' => 'Done',
                'message' => 'Lie angle updated successfully.',
                'type' => 'success',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect(url('lie-angles'))->with([
                'title' => 'Fail',
                'message' => 'Update failed: ' . $e->getMessage(),
                'type' => 'error',
            ]);
        }
    }

    public function destroy($id)
    {
        try {
            Lie::where('LieId', $id)->delete();
            return redirect(url('lie-angles'))->with(['title' => 'Done', 'message' => 'Lie angle deleted successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            return redirect(url('lie-angles'))->with(['title' => 'Fail', 'message' => 'Unable to delete Lie angle.', 'type' => 'error']);
        }

    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $gripSize = Lie::where('LieId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $gripSize->status = $request->status;
            $gripSize->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Lie angle status updated successfully.',
                'status' => $gripSize->status,
                'resultHtml' => ($gripSize->status == 1) ? '<span style="cursor:pointer;" class="success change-grip-size-status"  lie_id="'.$gripSize->LieId.'" lie_status="0" >Active</span>' : '<span class="danger change-grip-size-status" lie_id="'.$gripSize->LieId.'" lie_status="1"  style="cursor:pointer;">Inactive</span>'

            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update Lie angle status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


}
