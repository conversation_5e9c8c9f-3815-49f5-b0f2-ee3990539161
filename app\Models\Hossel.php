<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Hossel extends Model
{
    use HasFactory;
    use softDeletes;

    protected $guarded = [];

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-hossel-status"  hossel_id="' . $this->id . '" hossel_status="0" >Active</span>' : '<span class="danger change-grip-size-status" hossel_id="' . $this->id . '" hossel_status="1"  style="cursor:pointer;">Inactive</span>';
    }
    public function category(){
        return $this->hasOne(ClbType::class,'ClbTypeCd', 'category_id');
    }
}

