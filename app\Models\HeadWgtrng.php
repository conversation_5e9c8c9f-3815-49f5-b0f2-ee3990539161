<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class HeadWgtrng extends Model
{
    use HasFactory;
    use softDeletes;
    protected $primaryKey = 'HeadWgtRngCd';
    public $incrementing = false;
    protected $keyType ='string';
    protected $table = 'headwgtrng';
    protected $guarded = [];
    protected $appends = ['StatusHtml'];

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-weight-status"  model_id="'.$this->HeadWgtRngCd.'" model_status="0" >Active</span>' : '<span class="danger change-model-status" model_id="'.$this->HeadWgtRngCd.'" model_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
