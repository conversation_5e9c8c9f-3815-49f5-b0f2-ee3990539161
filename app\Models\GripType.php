<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class GripType extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'griptype';
    protected $guarded = [];
    protected $primaryKey = 'GripTypeId';

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-grip-type-status"  grip_type_id="'.$this->GripTypeId.'" grip_type_status="0" >Active</span>' : '<span class="danger change-grip-type-status" grip_type_id="'.$this->GripTypeId.'" grip_type_status="1"  style="cursor:pointer;">Inactive</span>';
    }



/*span.success
span.danger
span.yellow
text_success
text_danger
text_yellow*/

}
