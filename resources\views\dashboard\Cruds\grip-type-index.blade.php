@extends('theme.layout.master')
@push('css')
    <style>
        .tooltip-inner img {
            width: 150px; /* Control the size of the image preview */
            height: auto;
        }
    </style>
@endpush
@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>CRUDs > Grip Types</h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_action_fields">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                                <!-- Filter Dropdown -->
                                <div class="dropdown-btn">
                                    <button type="button" class="btn dropdown-toggle light_green_btn"
                                            id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                    <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                        <div class="dropdown_top">
                                            <h6 class="">Filter</h6>
                                            <button type="button" class="btn_close" data-bs-dismiss="dropdown"
                                                    aria-label="Close">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        @include('theme.layout.partials.status_category_form_filter',['status'=>true])
                                    </div>
                                </div>
                                <!-- Create Button -->
                                <a href="{{url('grip-types/create')}}" class="btn dark_green_btn"><i
                                        class="fa-solid fa-square-plus"></i>Create</a>
                                </div>
                            </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success">  <i class="fas fa-check-circle"></i> </button>
                                <button id="deactivateSelected" class="btn btn-danger">     <i class="fas fa-ban"></i> </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="grip-type-datatable table table-row-bordered gy-5 custom_sizing"
                                   id="gripTypeTable">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Short Code</th>
                                    <th>Grip Type</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal fade custom_modal" id="update_grip_type" tabindex="-1" aria-labelledby="createModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Edit Grip Type</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                            class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="grip_type_form_update" action="{{ route('grip-types.update', 0) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="grip_type_id_update" id="grip_type_id_update">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Grip Type</label>
                                    <input type="text" class="form-control" id="grip_type_dsc_update" name="grip_type_dsc_update" placeholder="Type Here" value="{{ old('grip_type_dsc_update') }}">
                                    <div class="text-danger" id="grip_type_dsc_error"></div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Grip Price</label>
                                    <input type="text" class="form-control" id="grip_type_price_update" name="grip_type_price_update" placeholder="Type Here" value="{{ old('grip_type_price_update') }}" oninput="formatPrice(this)">
                                    <div class="text-danger" id="grip_price_dsc_error"></div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Status</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="" selected disabled>Select Status</option>
                                        <option value="1" class="active_status" >Active</option>
                                        <option value="0" class="inactive_status">Inactive</option>
                                    </select>
                                    <div class="text-danger" id="status_error"></div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Update</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script>

        $('#activateSelected, #deactivateSelected').on('click', function () {
            let status = $(this).attr('id') === 'activateSelected' ? 1 : 0;
            let selectedIds = [];

            $('.category-checkbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
            console.log(selectedIds , status);

            if (selectedIds.length > 0) {
                $.ajax({
                    url: '{{ route('toggle.status') }}',
                    method: 'POST',
                    data: {
                        ids: selectedIds,
                        status: status,
                        primary_key: 'GripTypeId',
                        model: 'GripType',
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Status Updated!',
                                text: 'The selected categories have been successfully updated.',
                                confirmButtonText: 'OK'
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'An error occurred while updating the status. Please try again later.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Nothing Selected',
                    text: 'Please select at least one category to activate/deactivate.',
                    confirmButtonText: 'OK'
                });
            }
        });

        $(document).ready(function () {

            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);

            var table = $('.grip-type-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('grip-types.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.status = $('#status').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'checkbox', name: 'checkbox' },
                    { data: null, name: null, defaultContent: '' },
                    { data: 'short_code', name: 'short_code' },
                    { data: 'grip_type', name: 'grip_type' },
                    { data: 'price', name: 'price' },
                    { data: 'status', name: 'status' },
                    { data: 'action', name: 'action', orderable: false, searchable: false }
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                initComplete: function (settings, json) {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            document.querySelector('#dt-length-0').addEventListener('change', function () {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw();
                } else {
                    table.page.len(parseInt(selectedValue)).draw();
                }
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });


            // Select All functionality
            $('.select_all_checkboxes').on('click', function () {
                var checkboxes = $('.category-checkbox');
                var isChecked = $(this).prop('checked');
                checkboxes.prop('checked', isChecked);
            });

            $(document).on('change', '.category-checkbox', function () {
                var checkboxes = $('.category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', allChecked);
            });
        });

    </script>

    <script>
        $(document).on('click', '.edit_grip_type_button', function () {
            var gripTypeId = $(this).attr('grip_type_id');
            var gripTypeDsc = $(this).attr('grip_type_name');
            var gripTypeStatus = $(this).attr('grip_type_status'); // Value for status
            var gripTypePrice = $(this).attr('grip_type_price'); // Value for price

            $('#grip_type_id_update').val(gripTypeId);
            $('#grip_type_dsc_update').val(gripTypeDsc);
            $('#grip_type_price_update').val(gripTypePrice);
            if(gripTypeStatus == '1'){
                $('#status ').find('.active_status').attr('selected',true);
            }else{
                $('#status ').find('.inactive_status').attr('selected',true);
            }

            $('#update_grip_type').modal('show');
        });


        $(document).ready(function () {
            // Validate the form on submit
            $('#grip_type_form_update').validate({
                rules: {
                    grip_type_dsc_update: {
                        required: true
                    }
                },
                messages: {
                    grip_type_dsc_update: "Please enter a grip type"
                },
                submitHandler: function (form) {
                    form.submit();  // Form will be submitted if it's valid
                }
            });
        });

        $(document).on('click', '.change-grip-type-status', function () {
            var gripTypeId = $(this).attr('grip_type_id');
            var currentStatus = $(this).attr('grip_type_status');
            var currentElement = $(this);

            // Confirm the action
            Swal.fire({
                title: 'Are you sure?',
                text: 'You are about to change the status.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var url = "{{ url('grip-types') }}/" + gripTypeId + "/status";
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: currentStatus
                        },
                        success: function (response) {
                            if (response.success) {
                                currentElement.html(response.resultHtml);
                                currentElement.removeClass().addClass(response.resultHtml);
                                swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 4000,
                                });
                            } else {
                                swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                    timer: 4000,
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while changing the status.',
                                timer: 4000,
                            });
                        }
                    });
                }
            });
        });

    </script>
@endpush
