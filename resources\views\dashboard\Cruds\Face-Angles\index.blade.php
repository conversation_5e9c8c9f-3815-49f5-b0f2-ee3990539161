@extends('theme.layout.master')
@push('css')

@endpush
@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>CRUDs > Face Angle</h5>
                            </div>
                            <div class="side_fields">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                                <!-- Create Button -->
                                <button type="button" data-bs-toggle="modal" data-bs-target="#create_model"
                                        id="create_model_button" class="btn dark_green_btn"><i
                                        class="fa-solid fa-square-plus"></i>Create
                                </button>
                            </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success"><i
                                        class="fas fa-check-circle"></i></button>
                                <button id="deactivateSelected" class="btn btn-danger"><i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="models-datatable table table-row-bordered gy-5 custom_sizing" id="modelTable">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Face Angle Cd</th>
                                    <th>Face Angle Name</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Create Modal -->
    <div class="modal fade custom_modal" id="create_model" tabindex="-1" aria-labelledby="createModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Create Face Angle</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                            class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form" action="{{ url('face-angles') }}" method="POST"
                          enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Face Angle Name*</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="FaceAngleDsc"
                                           id="FaceAngleDsc" required value="{{ old('FaceAngleDsc') }}">
                                    @error('FaceAngleDsc')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Face Angle Price</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="FaceAnglePrice" id="FaceAnglePrice" value="{{ old('FaceAnglePrice') }}" oninput="formatPrice(this)">
                                    @error('FaceAnglePrice')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Create</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade custom_modal" id="update_model" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Edit Face Angle</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form_update" action="{{ route('face-angles.update', 0) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="model_id_update" id="model_id_update">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Face Angle Name</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="FaceAngleDsc_update" id="FaceAngleDsc_update" required value="{{ old('FaceAngleDsc') }}">
                                    @error('FaceAngleDsc_update')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Face Angle Price</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="FaceAnglePrice_update" id="FaceAnglePrice_update" value="{{ old('FaceAnglePrice_update') }}" oninput="formatPrice(this)">
                                    @error('FaceAnglePrice_update')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Update</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')

    <script>

        $('#activateSelected, #deactivateSelected').on('click', function () {
            let status = $(this).attr('id') === 'activateSelected' ? 1 : 0;
            let selectedIds = [];

            $('.category-checkbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
            console.log(selectedIds, status);

            if (selectedIds.length > 0) {
                $.ajax({
                    url: '{{ route('toggle.status') }}',
                    method: 'POST',
                    data: {
                        ids: selectedIds,
                        status: status,
                        primary_key: 'FaceAngleId',
                        model: 'FaceAngle',
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Status Updated!',
                                text: 'The selected categories have been successfully updated.',
                                confirmButtonText: 'OK'
                            }).then(function () {
                                location.reload();
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'An error occurred while updating the status. Please try again later.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Nothing Selected',
                    text: 'Please select at least one category to activate/deactivate.',
                    confirmButtonText: 'OK'
                });
            }
        });

        $(document).on('click', '.change-face-status', function () {
            var modelId = $(this).attr('faceangle_id');
            var status = $(this).attr('faceangle_status');
            var current_parent = $(this).parent();
            Swal.fire({
                title: 'Are you sure?',
                text: 'You are about to change the status.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var url = "{{ url('face-angles') }}/" + modelId + "/status";
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: status
                        },
                        success: function (response) {
                            if (response.success) {
                                current_parent.html(response.resultHtml);
                                swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 4000,
                                });
                            } else {
                                swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                    timer: 4000,

                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while changing the status.',
                                timer: 4000,
                            });
                        }
                    });
                }
            });
        });

        $(document).on('click', '.edit_model_button', function () {
            const modelId = $(this).attr('model_id');
            const modelFaceAngleDsc = $(this).attr('model_FaceAngleDsc');
            const modelFaceAnglePrice = $(this).attr('model_FaceAnglePrice');

            $('#model_id_update').val(modelId);
            $('#FaceAngleDsc_update').val(modelFaceAngleDsc);
            $('#FaceAnglePrice_update').val(modelFaceAnglePrice);

            $('#update_model').modal('show');

        });

    </script>

    <script>

        $(document).on("click", ".close-btn", function () {
            var parentDiv = $(this).parent();
            parentDiv.remove();

            if ($(".code_value span").length === 0) {
                $(".code_value").hide();
            }

        });

    </script>

    <script>
        $(document).ready(function () {

            var table = $('.models-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('face-angles.index') }}',
                    type: 'GET',
                    data: function (d) {
                    },
                    dataSrc: 'data'
                },
                columns: [
                    {data: 'checkbox', name: 'checkbox'},
                    {data: null, name: null, defaultContent: ''},
                    {data: 'FaceAngleCd', name: 'FaceAngleCd'},
                    {data: 'FaceAngleDsc', name: 'FaceAngleDsc'},
                    {data: 'price', name: 'price'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 15,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                initComplete: function (settings, json) {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Select All functionality
            $('.select_all_checkboxes').on('click', function () {
                var checkboxes = $('#modelTable tbody .category-checkbox');
                var isChecked = $(this).prop('checked'); // Get the state of select_all_checkboxes
                checkboxes.prop('checked', isChecked); // Set all category-checkbox to the same state
            });

            // Update select_all_checkboxes when any category-checkbox is changed
            $(document).on('change', '#modelTable tbody .category-checkbox', function () {
                var checkboxes = $('#modelTable tbody .category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', allChecked); // Update select_all_checkboxes
            });

            // Ensure select_all_checkboxes state is updated after table redraw
            table.on('draw', function () {
                var checkboxes = $('#modelTable tbody .category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', allChecked);
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

            document.querySelector('#dt-length-0').addEventListener('change', function () {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw();
                } else {
                    table.page.len(parseInt(selectedValue)).draw();
                }
            });
        });


    </script>

@endpush
