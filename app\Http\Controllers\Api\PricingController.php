<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ClubPricingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PricingController extends Controller
{
    protected $pricingService;

    public function __construct(ClubPricingService $pricingService)
    {
        $this->pricingService = $pricingService;
    }

    /**
     * Calculate price for a single club configuration
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateClubPrice(Request $request): JsonResponse
    {
        try {
            $clubData = $request->all();
            
            // Validate required model data
            if (!isset($clubData['category']['model']['model_id'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Model ID is required for pricing calculation'
                ], 400);
            }

            $price = $this->pricingService->calculateClubPrice($clubData);
            $breakdown = $this->pricingService->getPricingBreakdown($clubData);

            return response()->json([
                'success' => true,
                'data' => [
                    'total_price' => $price,
                    'breakdown' => $breakdown
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating price: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate total price for multiple clubs
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateOrderTotal(Request $request): JsonResponse
    {
        try {
            $clubs = $request->input('clubs', []);
            
            if (empty($clubs)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No clubs provided for pricing calculation'
                ], 400);
            }

            $totalPrice = $this->pricingService->calculateOrderTotal($clubs);
            
            // Get breakdown for each club
            $clubBreakdowns = [];
            foreach ($clubs as $index => $club) {
                $clubBreakdowns[$index] = [
                    'club_price' => $this->pricingService->calculateClubPrice($club),
                    'quantity' => $club['category']['model']['quantity'] ?? 1,
                    'breakdown' => $this->pricingService->getPricingBreakdown($club)
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'total_order_amount' => $totalPrice,
                    'clubs' => $clubBreakdowns
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating order total: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pricing breakdown for debugging purposes
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPricingBreakdown(Request $request): JsonResponse
    {
        try {
            $clubData = $request->all();
            
            if (!isset($clubData['category']['model']['model_id'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Model ID is required for pricing breakdown'
                ], 400);
            }

            $breakdown = $this->pricingService->getPricingBreakdown($clubData);

            return response()->json([
                'success' => true,
                'data' => $breakdown
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting pricing breakdown: ' . $e->getMessage()
            ], 500);
        }
    }
}
