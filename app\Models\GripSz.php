<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class GripSz extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'gripsz';
    protected $guarded = [];
    protected $primaryKey = 'GripSzId';

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-grip-size-status"  grip_size_id="'.$this->GripSzId.'" grip_size_status="0" >Active</span>' : '<span class="danger change-grip-size-status" grip_size_id="'.$this->GripSzId.'" grip_size_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
