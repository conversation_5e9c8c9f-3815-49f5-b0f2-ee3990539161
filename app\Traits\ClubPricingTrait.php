<?php

namespace App\Traits;

use App\Services\ClubPricingService;

trait ClubPricingTrait
{
    /**
     * Get pricing service instance
     *
     * @return ClubPricingService
     */
    protected function getPricingService(): ClubPricingService
    {
        return new ClubPricingService();
    }

    /**
     * Calculate price for a single club
     *
     * @param array $clubData
     * @return float
     */
    protected function calculateClubPrice(array $clubData): float
    {
        return $this->getPricingService()->calculateClubPrice($clubData);
    }

    /**
     * Calculate total order amount
     *
     * @param array $clubs
     * @return float
     */
    protected function calculateOrderTotal(array $clubs): float
    {
        return $this->getPricingService()->calculateOrderTotal($clubs);
    }

    /**
     * Get pricing breakdown for debugging
     *
     * @param array $clubData
     * @return array
     */
    protected function getPricingBreakdown(array $clubData): array
    {
        return $this->getPricingService()->getPricingBreakdown($clubData);
    }

    /**
     * Update order and club details with calculated prices
     *
     * @param object $order
     * @param array $clubs
     * @return void
     */
    protected function updateOrderPricing($order, array $clubs): void
    {
        $pricingService = $this->getPricingService();
        
        // Calculate and update total order amount
        $totalAmount = $pricingService->calculateOrderTotal($clubs);
        $order->TtlAmt = $totalAmount;
        $order->save();

        // Update each club detail with individual pricing
        foreach ($clubs as $clubIndex => $club) {
            $clubPrice = $pricingService->calculateClubPrice($club);
            $quantity = $club['category']['model']['quantity'] ?? 1;
            
            // Find club details for this club and update pricing
            $clubDetails = $order->clubDetail()
                ->where('ClbTypeCd', $club['category']['category_id'] ?? null)
                ->where('HeadDsgnId', $club['category']['model']['model_id'] ?? null)
                ->get();
            
            foreach ($clubDetails as $clubDetail) {
                $clubDetail->EachAmt = $clubPrice;
                $clubDetail->save();
            }
        }
    }
}
