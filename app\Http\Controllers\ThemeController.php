<?php

namespace App\Http\Controllers;

use App\Models\ClbType;
use App\Models\CmsAbout;
use App\Models\CmsContact;
use App\Models\CmsFitterPage;
use App\Models\CmsHome;
use App\Models\CmsHGPage;
use App\Models\FaqSection;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Models\StickTab;


class ThemeController extends Controller{
    function __construct()
    {

    }
    public function dashboard(){
        if (auth()->user()->hasRole('fitter')) {
            return redirect(url(''));
        }elseif (auth()->user()->hasRole('customer')){
            return redirect(url(''));
        }
    	return view('theme.index');
    }//end dashboard function.
    public function permissions(){
    	return view('theme.user-management.permissions');
    }//end permissions function.


    public function fitters(){
        return view('dashboard.user-management.Fitters.fitters');
    }
    public function createFitters(){
        return view('dashboard.user-management.Fitters.create');
    }
    public function editFitters(){
        return view('dashboard.user-management.Fitters.edit');
    }
    public function viewFitters(){
        return view('dashboard.user-management.Fitters.view');
    }
    public function orderView(){
        return view('dashboard.user-management.Fitters.order-view');
    }
//    public function staff(){
//        return view('dashboard.user-management.Staff.index');
//    }
//    public function createStaff(){
//        return view('dashboard.user-management.Staff.create');
//    }
//    public function editStaff(){
//        return view('dashboard.user-management.Staff.edit');
//    }
//    public function viewStaff(){
//        return view('dashboard.user-management.Staff.view');
//    }
//    public function customers(){
//        return view('dashboard.user-management.Customers.index');
//    }
//    public function viewCustomers(){
//        return view('dashboard.user-management.Customers.view');
//    }

    public function product(){
        return view('dashboard.product-management.Product.index');
    }
    public function viewProduct(){
        return view('dashboard.product-management.Product.view');
    }
    public function createProduct(){
        return view('dashboard.product-management.Product.create-product');
    }
    public function stockIn(){
        return view('dashboard.product-management.Stock-In.index');
    }
    public function viewStockin(){
        return view('dashboard.product-management.Stock-In.view');
    }
    public function stockOut(){
        return view('dashboard.product-management.Stock-Out.index');
    }
    public function viewStockout(){
        return view('dashboard.product-management.Stock-Out.view');
    }
    public function quotation(){
        return view('dashboard.order-management.Quotation.index');
    }
    public function viewQuotation(){
        return view('dashboard.order-management.Quotation.view');
    }
    public function purchased(){
        return view('dashboard.order-management.Purchased.index');
    }
    public function viewPurchased(){
        return view('dashboard.order-management.Purchased.view');
    }
    public function completed(){
        return view('dashboard.order-management.Completed.index');
    }
    public function viewCompleted(){
        return view('dashboard.order-management.Completed.view');
    }

    public function putterIndex(){
        return view('dashboard.Cruds.putter-index');
    }
    public function createPutter(){
        return view('dashboard.Cruds.create-putter');
    }
    public function crudChargeCode(){
        return view('dashboard.Cruds.crud-charge-code');
    }
    public function crudBillCode(){
        return view('dashboard.Cruds.crud-bill-code');
    }
    public function notification(){
        return view('dashboard.Notification.index');
    }
    public function cms(){
        $setting = Setting::first();
        $faqs = FaqSection::get();
        $cmsHome = CmsHome::first();
        $cmsAbout = CmsAbout::first();
        $cmsContact = CmsContact::first();
        $cmshomeTabs = StickTab::take(3)->get();
        $cmsHGprocess = CmsHGPage::first();
        $cmsBecomeAFitter = CmsFitterPage::first();
        return view('dashboard.cms.index', compact('setting','faqs','cmsHome','cmsAbout','cmsContact', 'cmshomeTabs', 'cmsHGprocess', 'cmsBecomeAFitter'));
    }
    public function cmsFaqSubmit(Request $request){
        if (isset($request->faq) && is_array($request->faq)) {
            if (FaqSection::exists()) {
                FaqSection::query()->delete();
            }
            foreach ($request->faq as $faqData) {
                if (isset($faqData['title']) && isset($faqData['description'])) {
                    FaqSection::create([
                        'title' => $faqData['title'],
                        'description' => $faqData['description'],
                    ]);
                }
            }
        }
        return redirect()->back()->with(['title'=>'Done','message'=>'Common Setting Update successfully','type'=>'success']);
    }

    public function toggleStatus(Request $request)
    {
        if ($request->model == "OrdType" || $request->model == "ShipVia"){
            $status = $request->CurrInd;
            $primaryKey = $request->primary_key;
            $ids = $request->ids;
            $model = $request->model;
            $modelInstance = app("App\Models\\$model");
            $modelInstance::whereIn($primaryKey, $ids)->update(['CurrInd' => $status]);
        }else{
            $status = $request->status;
            $primaryKey = $request->primary_key;
            $ids = $request->ids;
            $model = $request->model;
            $modelInstance = app("App\Models\\$model");
            $modelInstance::whereIn($primaryKey, $ids)->update(['status' => $status]);
        }
        return response()->json(['success' => true]);
    }

}


