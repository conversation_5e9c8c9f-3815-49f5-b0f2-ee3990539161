<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\GripType;
use DB;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class GripTypesController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $gripTypesQuery = GripType::latest();

            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $gripTypesQuery->where(function ($query) use ($searchValue) {
                    $query->where('GripTypeDsc', 'like', "%$searchValue%")
                        ->orWhere('GripTypeCd', 'like', "%$searchValue%");
                });
            }

            if ($request->has('status') && $request->status != '') {
                $gripTypesQuery->where('status', $request->status == 'active' ? '1' : '0');
            }

            return DataTables::of($gripTypesQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->GripTypeId . '">';
                })
                ->addColumn('short_code', function ($row) {
                    return $row->GripTypeCd;
                })
                ->addColumn('grip_type', function ($row) {
                    return $row->GripTypeDsc;
                })
                ->addColumn('price', function ($row) {
                    return $row->price??'0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml;
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item edit_grip_type_button"
                                   href="javascript:void(0)"
                                   grip_type_id="' . $row->GripTypeId . '"
                                   grip_type_name="' . $row->GripTypeDsc . '"
                                   grip_type_short_code="' . $row->GripTypeCd . '"
                                   grip_type_price="' . $row->price . '"
                                   grip_type_status="' . $row->status . '">Edit</a></li>
                            <li>
                                <form action="' . route('grip-types.destroy', $row->GripTypeId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . '
                                    ' . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>
                ';
                })
                ->rawColumns(['status', 'action','checkbox'])
                ->make(true);
        }

        return view('dashboard.Cruds.grip-type-index');
    }

    public function create()
    {
        return view('dashboard.Cruds.create-grip-type');
    }

    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'grip_type' => 'required',
        ], [
            'grip_type.required' => 'Please enter a grip type.',
        ]);
        try {
            DB::beginTransaction();
            GripType::create([
                'GripTypeCd' => strtoupper(implode('', array_map(fn($word) => strtoupper($word[0]), explode(' ', $request->grip_type)))),
                'GripTypeDsc' => $request->grip_type,
                'price' =>  isset($request->grip_price)  ? $request->grip_price : 0,
            ]);
            DB::commit();
            return redirect(url('grip-types'))->with([
                'title' => "Done",
                'message' => "grip type created successfully.",
                'type' => "success"
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect(url('grip-types'))->with(['title' => 'Fail', 'message' => 'Unable to create grip type.', 'type' => 'error']);
        }

    }

    public function show($id)
    {

    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request)
    {
        $request->validate([
            'grip_type_dsc_update' => 'required'
        ]);

        try {
            DB::beginTransaction();

            $gripType = GripType::find($request->grip_type_id_update);

            if (!$gripType) {
                throw new \Exception('Grip type not found.');
            }

            if ($gripType->GripTypeDsc !== $request->grip_type_dsc_update) {
                if (GripType::where('GripTypeDsc', $request->grip_type_dsc_update)->exists()) {
                    throw new \Exception('This grip type description already exists.');
                }
            }

            $gripType->update([
                'GripTypeDsc' => $request->grip_type_dsc_update,
                'GripTypeCd' => strtoupper(implode('', array_map(fn($word) => strtoupper($word[0]), explode(' ', $request->grip_type_dsc_update)))),
                'price' =>  isset($request->grip_type_price_update)  ? $request->grip_type_price_update : 0,
                'status' => $request->status,

            ]);

            DB::commit();
            return redirect('grip-types')->with(['title' => 'Success', 'message' => 'Grip type updated successfully!']);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect('grip-types')->with(['title' => 'Fail', 'message' => $e->getMessage(), 'type' => 'error']);
        }
    }

    public function destroy($id)
    {
        try {
            GripType::where('GripTypeId', $id)->update(['deleted_at' => now()]);
            return redirect(url('grip-types'))->with(['title' => 'Done', 'message' => 'Grip type deleted successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            return redirect(url('shafts'))->with(['title' => 'Fail', 'message' => 'Unable to delete grip type, please try again.', 'type' => 'error']);
        }
    }


    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $gripType = GripType::where('GripTypeId', $id)->firstOrFail();  // Assuming you want to use $gripType

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $gripType->status = $request->status;  // Use $gripType here
            $gripType->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Grip Type status updated successfully.',
                'status' => $gripType->status,
                'resultHtml' => $gripType->status == 1
                    ? '<span style="cursor:pointer;" class="success change-grip-type-status" grip_type_id="' . $gripType->GripTypeId . '" grip_type_status="0">Active</span>'
                    : '<span class="danger change-grip-type-status" grip_type_id="' . $gripType->GripTypeId . '" grip_type_status="1" style="cursor:pointer;">Inactive</span>',
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update grip type status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    public function uploadShaftImages(Request $request)
    {
        try {
            $request->validate([
                'image' => 'required|min:1',
                'image.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            ], [
                'image.required' => 'Please upload at least one image.',
                'image.*.image' => 'Each file must be an image.',
                'image.*.mimes' => 'Only PNG, JPEG, JPG, and GIF images are allowed.',
                'image.*.max' => 'The image size must be less than 2MB.',
            ]);
            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully.',
                'image' => $this->storeImage('shafts', $request->file('image')),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to upload image, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }

    }
}
