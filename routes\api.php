<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Pricing API routes
Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('pricing')->group(function () {
        Route::post('/club', [App\Http\Controllers\Api\PricingController::class, 'calculateClubPrice']);
        Route::post('/order-total', [App\Http\Controllers\Api\PricingController::class, 'calculateOrderTotal']);
        Route::post('/breakdown', [App\Http\Controllers\Api\PricingController::class, 'getPricingBreakdown']);
    });
});
