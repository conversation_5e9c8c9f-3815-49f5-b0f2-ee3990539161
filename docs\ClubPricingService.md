# Club Pricing Service Documentation

## Overview

The Club Pricing Service handles all pricing calculations for golf club orders. It supports both B2B and B2C pricing models and calculates prices based on required and optional components.

## Components

### 1. ClubPricingService (`app/Services/ClubPricingService.php`)

Main service class that handles all pricing calculations.

### 2. ClubPricingTrait (`app/Traits/ClubPricingTrait.php`)

Trait that can be used in controllers to easily access pricing functionality.

### 3. PricingController (`app/Http/Controllers/Api/PricingController.php`)

API controller for real-time pricing calculations.

## Pricing Logic

### Required Components
- **Model**: Required for all clubs
  - B2B pricing: Uses `b2b_price` column
  - B2C pricing: Uses `b2c_price` column
  - Both pricing: Uses appropriate price based on user type

### Optional Components (can be null)
- **Grip Type**: Additional cost from `griptype.price`
- **Grip Size**: Additional cost from `gripsz.price`
- **Loft**: Additional cost from `loft.price`
- **Lie**: Additional cost from `lie.price`
- **Face Angle**: Additional cost from `faceangle.price`
- **Hossel**: Additional cost from `hossels.price`

### B2B vs B2C Determination
- **B2B**: User has 'fitter' role
- **B2C**: User has 'customer' role or other roles
- Default: B2C if no user is authenticated

## Usage Examples

### Basic Usage in Controller

```php
use App\Services\ClubPricingService;

class OrderController extends Controller
{
    public function calculatePrice(Request $request)
    {
        $pricingService = new ClubPricingService();
        
        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => 1,
                    'loft_id' => 2,
                    'lie_angle_id' => 3
                ]
            ],
            'grip' => [
                'type' => 1,
                'size' => 2
            ]
        ];
        
        $price = $pricingService->calculateClubPrice($clubData);
        $breakdown = $pricingService->getPricingBreakdown($clubData);
        
        return response()->json([
            'price' => $price,
            'breakdown' => $breakdown
        ]);
    }
}
```

### Using the Trait

```php
use App\Traits\ClubPricingTrait;

class OrderController extends Controller
{
    use ClubPricingTrait;
    
    public function store(Request $request)
    {
        $clubs = $request->input('clubs');
        
        // Calculate total order amount
        $totalAmount = $this->calculateOrderTotal($clubs);
        
        // Create order
        $order = Order::create([
            'total_amount' => $totalAmount,
            // ... other fields
        ]);
        
        // Update pricing for all clubs
        $this->updateOrderPricing($order, $clubs);
    }
}
```

### API Endpoints

#### Calculate Club Price
```
POST /api/pricing/club
```

Request body:
```json
{
    "category": {
        "model": {
            "model_id": 1,
            "loft_id": 2
        }
    },
    "grip": {
        "type": 1,
        "size": 2
    }
}
```

Response:
```json
{
    "success": true,
    "data": {
        "total_price": 185.50,
        "breakdown": {
            "model_price": 150.00,
            "grip_type_price": 25.00,
            "grip_size_price": 10.50,
            "loft_price": 0.00,
            "total_price": 185.50,
            "is_b2b": true
        }
    }
}
```

#### Calculate Order Total
```
POST /api/pricing/order-total
```

Request body:
```json
{
    "clubs": [
        {
            "category": {
                "model": {
                    "model_id": 1,
                    "quantity": 2
                }
            }
        },
        {
            "category": {
                "model": {
                    "model_id": 2,
                    "quantity": 1
                }
            }
        }
    ]
}
```

## Database Integration

### Order Table (`ord`)
- `TtlAmt`: Total order amount calculated by the service

### Club Detail Table (`clbdtl`)
- `EachAmt`: Individual club price calculated by the service

## Testing

Run the pricing service tests:

```bash
php artisan test tests/Unit/ClubPricingServiceTest.php
```

## Error Handling

The service handles the following scenarios:
- Missing model ID (returns 0)
- Non-existent model (returns 0)
- Null optional component prices (treated as 0)
- Missing user authentication (defaults to B2C pricing)

## Performance Considerations

- Service uses direct model queries for pricing lookup
- Consider caching pricing data for high-volume applications
- Pricing calculations are performed in memory after data retrieval

## Future Enhancements

1. **Caching**: Add Redis caching for frequently accessed pricing data
2. **Bulk Operations**: Optimize for bulk pricing calculations
3. **Price History**: Track pricing changes over time
4. **Discounts**: Add support for promotional pricing and discounts
5. **Currency**: Add multi-currency support
