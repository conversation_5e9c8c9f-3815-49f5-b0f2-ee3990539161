<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Staff extends Model
{
    use HasFactory;

    protected $primaryKey = 'StaffId';
    protected $table = 'staff';

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function getFirstNameAttribute()
    {
        $staffName = $this->StaffNm ?? '';
        $nameParts = explode(' ', $staffName);

        return $nameParts[0] ?? '';
    }

    public function getLastNameAttribute()
    {
        $staffName = $this->StaffNm ?? '';
        $nameParts = explode(' ', $staffName);

        if (count($nameParts) > 1) {
            return implode(' ', array_slice($nameParts, 1));
        }

        return '';
    }

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-staff-status"  staff_id="' . $this->StaffId . '" staff_status="0" >Active</span>' : '<span class="danger change-staff-status" staff_id="' . $this->StaffId . '" staff_status="1"  style="cursor:pointer;">Inactive</span>';
    }

}
