<?php

namespace App\Http\Controllers;

use App\Models\GripSz;
use App\Models\GripType;
use Illuminate\Http\Request;
use DB;
use Yajra\DataTables\Facades\DataTables;

class GripSizesController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $gripSizesQuery = GripSz::latest();

            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $gripSizesQuery->where(function ($query) use ($searchValue) {
                    $query->where('GripSzCd', 'like', "%$searchValue%")
                        ->orWhere('GripSzDsc', 'like', "%$searchValue%");
                });
            }

            if ($request->has('status') && $request->status != '') {
                $gripSizesQuery->where('status', $request->status == 'active' ? '1' : '0');
            }

            return DataTables::of($gripSizesQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->GripSzId . '">';
                })
                ->addColumn('short_size', function ($row) {
                    return $row->GripSzCd;
                })
                ->addColumn('size', function ($row) {
                    return $row->GripSzDsc;
                })
                ->addColumn('price', function ($row) {
                    return $row->price??'0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml;
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item edit_grip_size_button"
                                   href="javascript:void(0)"
                                   data-id="' . $row->GripSzId . '"
                                   data-name="' . $row->GripSzDsc . '"
                                   data-price="' . $row->price . '"
                                   data-code="' . $row->GripSzCd . '"
                                   data-status="' . $row->status . '">Edit</a></li>
                            <li>
                                <form action="' . route('grip-sizes.destroy', $row->GripSzId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . '
                                    ' . method_field('DELETE') . '
                                        <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>
                ';
                })
                ->rawColumns(['status', 'action','checkbox'])
                ->make(true);
        }

        return view('dashboard.Cruds.grip-size');
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('dashboard.Cruds.create-grip-size');

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'grip_size' => 'required',
        ], [
            'grip_size.required' => 'Please enter a grip size.',
        ]);
        try {
            DB::beginTransaction();
            GripSz::create([
                'GripSzCd' => strtoupper(implode('', array_map(fn($word) => strtoupper($word[0]), explode(' ', $request->grip_size)))),
                'GripSzDsc' => $request->grip_size,
                'price' => isset($request->grip_price)  ? $request->grip_price : 0,
                'status' => 1,
            ]);
            DB::commit();
            return redirect(url('grip-sizes'))->with([
                'title' => "Done",
                'message' => "grip size created successfully.",
                'type' => "success",
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect(url('grip-sizes'))->with(['title' => 'Fail', 'message' => 'Unable to create grip size.', 'type' => 'error']);
        }

    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        $request->validate([
            'grip_size_dsc_update' => 'required',
        ]);

        try {
            DB::beginTransaction();

            $gripSize = GripSz::find($request->grip_sz_id_update);

            if (!$gripSize) {
                throw new \Exception('Grip size not found.');
            }

            if ($gripSize->GripSzDsc !== $request->grip_size_dsc_update) {
                if (GripSz::where('GripSzDsc', $request->grip_size_dsc_update)->exists()) {
                    throw new \Exception('This grip size description already exists.');
                }
            }

            $gripSize->update([
                'GripSzDsc' => $request->grip_size_dsc_update,
                'GripSzCd' => strtoupper(implode('', array_map(fn($word) => strtoupper($word[0]), explode(' ', $request->grip_size_dsc_update)))),
                'price' => isset($request->grip_size_price_update)  ? $request->grip_size_price_update : 0,
                'status' => $request->status,
            ]);

            DB::commit();
            return redirect('grip-sizes')->with(['title' => 'Success', 'message' => 'Grip size updated successfully!']);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect('grip-sizes')->with(['title' => 'Fail', 'message' => $e->getMessage(), 'type' => 'error']);
        }
    }


    /**
     * Remove the specified resource from storage.
     */

    public function destroy($id)
    {
        try {
            GripSz::where('GripSzId', $id)->update(['deleted_at' => now()]);
            return redirect(url('grip-sizes'))->with(['title' => 'Done', 'message' => 'Grip size deleted successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            return redirect(url('grip-sizes'))->with(['title' => 'Fail', 'message' => 'Unable to delete grip size, please try again.', 'type' => 'error']);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $gripSize = GripSz::where('GripSzId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $gripSize->status = $request->status;
            $gripSize->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Grip size status updated successfully.',
                'status' => $gripSize->status,
                'resultHtml' => ($gripSize->status == 1) ? '<span style="cursor:pointer;" class="success change-grip-size-status"  grip_size_id="'.$gripSize->GripSzId.'" grip_size_status="0" >Active</span>' : '<span class="danger change-grip-size-status" grip_size_id="'.$gripSize->GripSzId.'" grip_size_status="1"  style="cursor:pointer;">Inactive</span>'

            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update category status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

}
