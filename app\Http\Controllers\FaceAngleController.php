<?php

namespace App\Http\Controllers;

use App\Models\FaceAngle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class FaceAngleController extends Controller
{

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $faceAngles = FaceAngle::latest()->get();

            return DataTables::of($faceAngles)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->FaceAngleId . '">';
                })
                ->addIndexColumn()
                ->addColumn('price', function ($row) {
                    return $row->price ?? '0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                       model_id="' . $row->FaceAngleId . '"
                                       model_FaceAngleDsc="' . $row->FaceAngleDsc . '"
                                       model_FaceAnglePrice="' . $row->price . '"
                                       model_FaceAngleCd="' . $row->FaceAngleCd . '">Edit</a></li>
                                <li>
                                <form action="' . route('face-angles.destroy', $row->FaceAngleId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['status','price','action','checkbox'])
                ->make(true);
        }

        return view('dashboard.Cruds.Face-Angles.index');
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        $request->validate([
            'FaceAngleDsc' => 'required',
        ], [
            'FaceAngleDsc.required' => 'FaceAngle angle name is required.',
            'FaceAngleDsc.unique' => 'FaceAngle angle name already exist.',
        ]);

        try {
            DB::beginTransaction();

            $words = explode(' ', $request->FaceAngleDsc);

            if (count($words) == 2) {
                $FaceAngleCd = strtoupper(substr($words[0], 0, 1)) . strtoupper(substr($words[1], 0, 1));
            } else {
                $FaceAngleCd = strtoupper(substr($request->FaceAngleDsc, 0, 2));
            }

            FaceAngle::create([
                'FaceAngleCd' => $FaceAngleCd ?? null,
                'FaceAngleDsc' => $request->FaceAngleDsc ?? null,
                'price' => isset($request->FaceAnglePrice)  ? $request->FaceAnglePrice : 0
            ]);

            DB::commit();

            return redirect(url('face-angles'))->with(['title' => 'Done', 'message' => 'FaceAngle angle created successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            DB::rollback();
            return redirect(url('face-angles'))->with(['title' => 'Fail', 'message' => 'Unable to create FaceAngle angle.', 'type' => 'error']);

        }

    }

    public function show($id)
    {
        //
    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request, $id)
    {
        $face = FaceAngle::where('FaceAngleId', $request->model_id_update)->firstOrFail();

        $request->validate([
            'FaceAngleDsc_update' => 'required',
        ], [
            'FaceAngleDsc_update.required' => 'FaceAngle angle name is required.',
            'FaceAngleDsc_update.unique' => 'FaceAngle angle name already exist.',
        ]);

        try {
            DB::beginTransaction();

            $words = explode(' ', $request->FaceAngleDsc_update);

            if (count($words) == 2) {
                $FaceAngleCd = strtoupper(substr($words[0], 0, 1)) . strtoupper(substr($words[1], 0, 1));
            } else {
                $FaceAngleCd = strtoupper(substr($request->FaceAngleDsc_update, 0, 2));
            }

            $face->update([
                'FaceAngleCd' => $FaceAngleCd ?? null,
                'FaceAngleDsc' => $request->FaceAngleDsc_update ?? null,
                'price' => isset($request->FaceAnglePrice_update)  ? $request->FaceAnglePrice_update : 0
            ]);

            DB::commit();

            return redirect(url('face-angles'))->with([
                'title' => 'Done',
                'message' => 'FaceAngle angle updated successfully.',
                'type' => 'success',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect(url('face-angles'))->with([
                'title' => 'Fail',
                'message' => 'Update failed: ' . $e->getMessage(),
                'type' => 'error',
            ]);
        }
    }

    public function destroy($id)
    {
        try {
            FaceAngle::where('FaceAngleId', $id)->delete();
            return redirect(url('face-angles'))->with(['title' => 'Done', 'message' => 'FaceAngle angle deleted successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            return redirect(url('face-angles'))->with(['title' => 'Fail', 'message' => 'Unable to delete FaceAngle angle.', 'type' => 'error']);
        }

    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $gripSize = FaceAngle::where('FaceAngleId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $gripSize->status = $request->status;
            $gripSize->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Face angle status updated successfully.',
                'status' => $gripSize->status,
                'resultHtml' => ($gripSize->status == 1) ? '<span style="cursor:pointer;" class="success change-grip-size-status"  lie_id="'.$gripSize->FaceAngleId.'" lie_status="0" >Active</span>' : '<span class="danger change-grip-size-status" lie_id="'.$gripSize->FaceAngleId.'" lie_status="1"  style="cursor:pointer;">Inactive</span>'

            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update face angle status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
