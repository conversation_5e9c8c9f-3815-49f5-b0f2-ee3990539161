@extends('theme.layout.master')
@push('css')
@endpush

@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Cutting Stations</h5>
                            </div>
                            <div class="side_fields">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="cutting-stations-datatable table table-row-bordered gy-5">
                                <thead>
                                <tr>
                                    <th>SR#</th>
                                    <th>Order No</th>
                                    <th>Customer</th>
                                    <th>Total Quantity</th>
                                    <th>Order Type</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection

@push('js')
    <script>
        $(document).ready(function () {
            var table = $('.cutting-stations-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('cuttingstations.index') }}',
                    type: 'GET',
                    dataSrc: 'data'
                },
                columns: [
                    {data: null, name: null, defaultContent: ''},
                    {data: 'OrdNo', name: 'OrdNo'},
                    {data: 'customer', name: 'customer'},
                    {data: 'total_qty', name: 'total_qty'},
                    {data: 'order_type', name: 'order_type'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(0)').html(dataIndex + 1);
                },
                initComplete: function () {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

            $(".filter-form").submit(function (e) {
                e.preventDefault();
                table.draw();
            });
        });

    </script>
@endpush
