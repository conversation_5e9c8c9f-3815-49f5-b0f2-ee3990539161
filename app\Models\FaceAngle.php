<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FaceAngle extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'faceangle';
    protected $guarded = [];
    protected $primaryKey = 'FaceAngleId';

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-face-status"  faceangle_id="'.$this->FaceAngleId.'" faceangle_status="0" >Active</span>' : '<span class="danger change-grip-size-status" faceangle_id="'.$this->FaceAngleId.'" faceangle_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
