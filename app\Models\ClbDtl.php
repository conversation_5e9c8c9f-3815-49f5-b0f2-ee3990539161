<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClbDtl extends Model
{
    use HasFactory , SoftDeletes;

    protected $table = 'clbdtl';
    protected $guarded = [];
    protected $primaryKey = 'ClbDtlId';

    public function order()
    {
        return $this->belongsTo(Ord::class , 'OrdId' , 'OrdId');
    }

    public function category()
    {
        return $this->belongsTo(ClbType::class , 'ClbTypeCd' , 'ClbTypeCd');
    }

    public function model()
    {
        return $this->belongsTo(HeadDsgn::class , 'HeadDsgnId' , 'HeadDsgnId');
    }

    public function shaft()
    {
        return $this->belongsTo(Shaft::class , 'ShaftId' , 'id');
    }
    public function lie()
    {
        return $this->belongsTo(Lie::class , 'LieId' , 'LieId');
    }

    public function faceAngle()
    {
        return $this->belongsTo(FaceAngle::class , 'FaceAngleId' , 'FaceAngleId');
    }
    public function loft()
    {
        return $this->belongsTo(Loft::class , 'LoftId' , 'LoftId');
    }
    public function hossel()
    {
        return $this->belongsTo(Hossel::class , 'HslId' , 'id');
    }

    public function shaftWeight()
    {
        return $this->belongsTo(HeadWgtrng::class , 'HeadWgtRngCd' , 'HeadWgtRngCd');
    }

    public function shaftFlex()
    {
        return $this->belongsTo(ShfFlx::class , 'ShfFlxCd' , 'ShfFlxCd');
    }

    public function shaftMaterial()
    {
        return $this->belongsTo(ShfType::class , 'ShfTypeCd' , 'ShfTypeCd');
    }
    public function shaftLength()
    {
        return $this->belongsTo(ShfLen::class , 'ShfLenId' , 'ShfLenCd');
    }
    public function gripType()
    {
        return $this->belongsTo(GripType::class , 'GripTypeId' , 'GripTypeId');
    }
    public function gripSize()
    {
        return $this->belongsTo(GripSz::class , 'TopGripSzId' , 'GripSzId');
    }

    public function cuttingStation()
    {
        return $this->belongsTo(CuttingStation::class , 'ClbDtlId' , 'club_id');
    }

    public function headingStation()
    {
        return $this->belongsTo(HeadingStation::class, 'ClbDtlId', 'club_id');
    }

    public function loftLieStation()
    {
        return $this->belongsTo(LoftLieStation::class, 'ClbDtlId', 'club_id');
    }

    public function sstPureStation()
    {
        return $this->belongsTo(SSTPuringStation::class, 'ClbDtlId', 'club_id');
    }

    public function epoxyStation()
    {
        return $this->belongsTo(EpoxyStation::class, 'ClbDtlId', 'club_id');
    }

    public function grippingStation()
    {
        return $this->belongsTo(GrippingStation::class, 'ClbDtlId', 'club_id');
    }

    public function finalCheckStation()
    {
        return $this->belongsTo(FinalCheckStation::class, 'ClbDtlId', 'club_id');
    }

    public function shippingStation()
    {
        return $this->belongsTo(ShippingStation::class, 'ClbDtlId', 'club_id');
    }

}
