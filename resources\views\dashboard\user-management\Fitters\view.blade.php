@extends('theme.layout.master')

@push('css')
@endpush

@section('content')
    <section class="view-dashboard view_profile_management">
        <div class="container-fluid">
            <div class="row custom_row_gap">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="profile_image">
                                    <img class="img-fluid"
                                         src="{{ asset('website') }}/{{ $fitter->user->profile->pic ?? 'fitters/default.png' }}">
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="user_info">
                                    <h1>{{ $fitter->FtrNmFirst ?? '-'}} {{ $fitter->FtrNmLast ?? '-'}}</h1>
                                    <ul class="list-unstyled">
                                        <li><i class="fa-solid fa-message"></i> {{ $fitter->EMailAddr ?? '-' }}</li>
                                        {!! $fitter->CellPhone ? '<li><i class="fa-solid fa-phone"></i> ' . $fitter->CellPhone . '</li>' : '' !!}
                                        <li><i class="fa-solid fa-user"></i> Fitter</li>
                                    </ul>
                                </div>
                                <div class="user_card_detail">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="custom_card">
                                                <img src="{{ asset('website') }}/assets/images/card-bar.svg">
                                                <h2>{{@$fitter->ftrOrders->count()??''}}</h2>
                                                <h5>Total Orders</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="custom_card">
                                                <img src="{{ asset('website') }}/assets/images/card-bar.svg">
                                                <h2>$10,000</h2>
                                                <h5>Total Commission</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="custom_card">
                                                <img src="{{ asset('website') }}/assets/images/card-bar.svg">
                                                <h2>$1k</h2>
                                                <h5>Total Order Value</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="custom_card">
                                                <img src="{{ asset('website') }}/assets/images/card-bar.svg">
                                                <h2>$1.5k</h2>
                                                <h5>Total Revenue</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="fitter_view_tabs">
                                    <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="pills-general-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-general" type="button" role="tab"
                                                    aria-controls="pills-general" aria-selected="true"><img
                                                    class="img-fluid"
                                                    src="{{ asset('website') }}/assets/images/information.svg"/>General
                                                Info
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-orders-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-orders" type="button" role="tab"
                                                    aria-controls="pills-orders" aria-selected="false"><img
                                                    class="img-fluid"
                                                    src="{{ asset('website') }}/assets/images/Orders.svg"/>Orders
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-customers-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-customers" type="button" role="tab"
                                                    aria-controls="pills-customers" aria-selected="false"><img
                                                    class="img-fluid"
                                                    src="{{ asset('website') }}/assets/images/user.svg"/>Customers
                                            </button>
                                        </li>
{{--                                        <li class="nav-item" role="presentation">--}}
{{--                                            <button class="nav-link" id="pills-permissions-tab" data-bs-toggle="pill"--}}
{{--                                                    data-bs-target="#pills-permissions" type="button" role="tab"--}}
{{--                                                    aria-controls="pills-permissions" aria-selected="false"><img--}}
{{--                                                    class="img-fluid"--}}
{{--                                                    src="{{ asset('website') }}/assets/images/List.svg"/>Permissions--}}
{{--                                            </button>--}}
{{--                                        </li>--}}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-general" role="tabpanel"
                                 aria-labelledby="pills-general-tab" tabindex="0">
                                <div class="user_general_info">
                                    <h3>General Information</h3>
                                    <div class="general_detail">
                                        <div class="txt_field">
                                            <label>E-Mail</label>
                                            <span>{{  $fitter->EMailAddr ?? '-'  }}</span>
                                        </div>
                                        <div class="txt_field">
                                            <label>Phone Number</label>
                                            <span>{{ $fitter->CellPhone ?? '-' }}</span>
                                        </div>
                                        <div class="txt_field">
                                            <label>Address</label>
                                            <span>{{ $fitter->Addr ?? '-' }}</span>
                                        </div>
                                        <div class="txt_field">
                                            <label>Account/Golf Course Name</label>
                                            <span>{{ $fitter->account->AcctNm ?? '-' }}</span>
                                        </div>
                                        <div class="txt_field">
                                            <label>Fitter Commission</label>
                                            <span>{{ $fitter->commission ?? '0' }}%</span>
                                        </div>
                                        <div class="txt_field">
                                            <label>Bio</label>
                                            <span>{{ $fitter->user->profile->bio ?? '-' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-orders" role="tabpanel" aria-labelledby="pills-orders-tab" tabindex="0">
                                <div class="table_header in_line">
                                    <div class="user_engagement">
                                        <h3>Order History</h3>
                                    </div>
                                    <div class="side_fields">
                                        <div class="custom_search_box">
                                            <form>
                                                <div class="txt_field">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                    <input type="search" placeholder="Search"
                                                           class="form-control searchinput">
                                                </div>
                                            </form>
                                        </div>
                                        <!-- Filter Dropdown -->
                                        <div class="dropdown-btn">
                                            <button type="button" class="btn dropdown-toggle light_green_btn"
                                                    id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-filter"></i> Filter
                                            </button>
                                            <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                                <div class="dropdown_top">
                                                    <h6 class="">Filter</h6>
                                                    <button type="button" class="btn_close" data-bs-dismiss="dropdown"
                                                            aria-label="Close">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <form>
                                                    <div class="form-group">
                                                        <label for="status">Status</label>
                                                        <select id="status" class="form-control" name="status">
                                                            <option value="">Select Status</option>
                                                            <option value="active">Active</option>
                                                            <option value="inactive">Inactive</option>
                                                            <option value="pending">Pending</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="order">Order Type</label>
                                                        <select id="order" class="form-control" name="order">
                                                            <option value="">Select Order</option>
                                                            <option value="admin">Admin</option>
                                                            <option value="user">User</option>
                                                            <option value="manager">Manager</option>
                                                        </select>
                                                    </div>
                                                    <div class="dropdown_bottom">
                                                        <button type="submit" class="btn light_green_btn">Apply Filter
                                                        </button>
                                                        <button type="button" class="btn cancel_btn"
                                                                data-bs-dismiss="dropdown">Cancel
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="datatable table">
                                        <thead>
                                        <tr>
                                            <th>SR#</th>
                                            <th>Order #</th>
                                            <th>Customer Name</th>
                                            <th>Order Type</th>
                                            <th>Bill Type</th>
                                            <th>Ship Via</th>
                                            <th>Total Amount</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($fitter->ftrOrders as $key => $order)
                                            <tr>
                                                <td>{{ $loop->iteration??$order->id }}</td>
                                                <td>{{$order->OrdNo??''}}</td>
                                                <td class="rounded-start">
                                                    <div class="">{{$order->customer->FirstNm??''}} {{$order->customer->LastNm??''}}</div>
                                                </td>
                                                <td>{{@$order->orderType->OrdTypeDsc??'-'}}</td>
                                                <td>{{@$order->billType->BillCdDsc??'-'}}</td>
                                                <td>{{@$order->shipViaType->ShipViaDsc??'-'}}</td>
                                                <td>${{ number_format($order->TtlAmt ?? 0, 2) }}</td>
                                                <td class="text_danger">{{$order->orderStatus->OrdStatDsc??'-'}}</td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <img class="ellipsis_img" src="{{ asset('website') }}/assets/images/ellipsis.svg">
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
{{--                                                            <li><a class="dropdown-item" href="{{url('order-view')}}">View</a></li>--}}
                                                            <li><a class="dropdown-item" href="{{url('orders/show')}}/{{$order->OrdId??''}}">View</a></li>
                                                            <li><a class="dropdown-item text_danger">Deactivate</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-customers" role="tabpanel"
                                 aria-labelledby="pills-customers-tab" tabindex="0">
                                <div class="table-responsive">
                                    <div class="table_header in_line">
                                        <div class="user_engagement">
                                            <h3>Customers</h3>
                                        </div>
                                        <div class="side_fields">
                                            <div class="custom_search_box">
                                                <form>
                                                    <div class="txt_field">
                                                        <i class="fa-solid fa-magnifying-glass"></i>
                                                        <input type="search" placeholder="Search" class="form-control searchinput">
                                                    </div>
                                                </form>
                                            </div>

                                        </div>
                                    </div>
                                    <table class="customer-datatable table">
                                        <thead>
                                        <tr>
                                            <th>SR#</th>
                                            <th>Customer Name</th>
                                            <th>Email</th>
                                            <th>Phone #</th>
                                            <th>Total Order</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                        </thead>
                                        <tbody>
{{--                                            @foreach($fitter->ftrCustomers as $key => $customer)--}}
{{--                                                <tr>--}}
{{--                                                    <td>{{ $loop->iteration??$customer->id }}</td>--}}
{{--                                                    <td class="rounded-start">--}}
{{--                                                        <div class="">{{$customer->FirstNm??''}} {{$customer->LastNm??''}}</div>--}}
{{--                                                    </td>--}}
{{--                                                    <td>{{$customer->EMailAddr??''}}</td>--}}
{{--                                                    <td>{{$customer->PhnNum??''}}</td>--}}
{{--                                                    <td>{{$customer->custOrders->count()??''}}</td>--}}
{{--                                                    <td class="text_yellow">Pending</td>--}}
{{--                                                    <td>--}}
{{--                                                        <div class="dropdown">--}}
{{--                                                            <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton12" data-bs-toggle="dropdown" aria-expanded="false">--}}
{{--                                                                <img class="ellipsis_img" src="{{ asset('website') }}/assets/images/ellipsis.svg">--}}
{{--                                                            </button>--}}
{{--                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton12">--}}
{{--                                                                <li><a class="dropdown-item" href="{{url('#!')}}">View</a></li>--}}
{{--                                                            </ul>--}}
{{--                                                        </div>--}}
{{--                                                    </td>--}}
{{--                                                </tr>--}}
{{--                                            @endforeach--}}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
{{--                            <div class="tab-pane fade" id="pills-permissions" role="tabpanel"--}}
{{--                                 aria-labelledby="pills-permissions-tab" tabindex="0">--}}
{{--                                <div class="fitter_permission">--}}
{{--                                    <h3>Permissions</h3>--}}
{{--                                    <div class="custom_accordion_grid">--}}
{{--                                        <div class="accordion accordion_grid_row" id="accordionPanelsStayOpenExample">--}}
{{--                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                <h6 class="accordion-header">--}}
{{--                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                        <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                               id="AccountsCheck">--}}
{{--                                                        <label class="form-check-label" for="AccountsCheck">--}}
{{--                                                            Accounts/GolfCourse--}}
{{--                                                        </label>--}}
{{--                                                    </div>--}}
{{--                                                    <button class="accordion-button" type="button"--}}
{{--                                                            data-bs-toggle="collapse"--}}
{{--                                                            data-bs-target="#panelsStayOpen-collapseOne"--}}
{{--                                                            aria-expanded="true"--}}
{{--                                                            aria-controls="panelsStayOpen-collapseOne">--}}
{{--                                                    </button>--}}
{{--                                                </h6>--}}
{{--                                                <div id="panelsStayOpen-collapseOne"--}}
{{--                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                    <div class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                        <div class="accordion_checkbox">--}}
{{--                                                            <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                                   id="AccountsView">--}}
{{--                                                            <label class="form-check-label" for="AccountsView">--}}
{{--                                                                View--}}
{{--                                                            </label>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion_checkbox">--}}
{{--                                                            <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                                   id="AccountsCreate">--}}
{{--                                                            <label class="form-check-label" for="AccountsCreate">--}}
{{--                                                                Create--}}
{{--                                                            </label>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion_checkbox">--}}
{{--                                                            <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                                   id="AccountsEdit">--}}
{{--                                                            <label class="form-check-label" for="AccountsEdit">--}}
{{--                                                                Edit--}}
{{--                                                            </label>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion_checkbox">--}}
{{--                                                            <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                                   id="AccountsDelete">--}}
{{--                                                            <label class="form-check-label" for="AccountsDelete">--}}
{{--                                                                Delete--}}
{{--                                                            </label>--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        <div class="accordion accordion_grid_row" id="accordionUserManagement">--}}
{{--                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                <h6 class="accordion-header semi_accordion_collapse">--}}
{{--                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                        <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                               id="UserManagementCheck">--}}
{{--                                                        <label class="form-check-label" for="UserManagementCheck">--}}
{{--                                                            User Management--}}
{{--                                                        </label>--}}
{{--                                                    </div>--}}
{{--                                                    <button class="accordion-button" type="button"--}}
{{--                                                            data-bs-toggle="collapse"--}}
{{--                                                            data-bs-target="#accordionUserManagementCollapse"--}}
{{--                                                            aria-expanded="true"--}}
{{--                                                            aria-controls="accordionUserManagementCollapse">--}}
{{--                                                    </button>--}}
{{--                                                </h6>--}}
{{--                                                <div id="accordionUserManagementCollapse"--}}
{{--                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                    <div class="accordion-body custom_accordion_body">--}}
{{--                                                        <div class="accordion" id="accordionFitters">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="FittersCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="FittersCheck">--}}
{{--                                                                            Fitters--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionFittersCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionFittersCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionFittersCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="FittersView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="FittersView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="FittersCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="FittersCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="FittersEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="FittersEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="FittersDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="FittersDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionStaff">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="StaffCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="StaffCheck">--}}
{{--                                                                            Staff--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionStaffCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionStaffCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionStaffCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaffView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaffView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaffCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaffCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaffEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaffEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaffDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaffDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionCustomers">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="CustomersCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="CustomersCheck">--}}
{{--                                                                            Customers--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionCustomersCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionCustomersCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionCustomersCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="CustomersView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="CustomersView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="CustomersCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="CustomersCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="CustomersEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="CustomersEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="CustomersDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="CustomersDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        <div class="accordion accordion_grid_row" id="accordionProductManagement">--}}
{{--                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                <h6 class="accordion-header semi_accordion_collapse">--}}
{{--                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                        <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                               id="ProductManagementCheck">--}}
{{--                                                        <label class="form-check-label" for="ProductManagementCheck">--}}
{{--                                                            Product Management--}}
{{--                                                        </label>--}}
{{--                                                    </div>--}}
{{--                                                    <button class="accordion-button" type="button"--}}
{{--                                                            data-bs-toggle="collapse"--}}
{{--                                                            data-bs-target="#accordionProductManagementCollapse"--}}
{{--                                                            aria-expanded="true"--}}
{{--                                                            aria-controls="accordionProductManagementCollapse">--}}
{{--                                                    </button>--}}
{{--                                                </h6>--}}
{{--                                                <div id="accordionProductManagementCollapse"--}}
{{--                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                    <div class="accordion-body custom_accordion_body">--}}
{{--                                                        <div class="accordion" id="accordionProduct">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="ProductCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="ProductCheck">--}}
{{--                                                                            Product--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionProductCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionProductCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionProductCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="ProductView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="ProductView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="ProductCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="ProductCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="ProductEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="ProductEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="ProductDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="ProductDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionStockIn">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="StockInCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="StockInCheck">--}}
{{--                                                                            Stock-In--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionStockInCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionStockInCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionStockInCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockInView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockInView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockInCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockInCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockInEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockInEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockInDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockInDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionStockOut">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="StockOutCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="StockOutCheck">--}}
{{--                                                                            Stock-Out--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionStockOutCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionStockOutCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionStockOutCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockOutView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockOutView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockOutCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockOutCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockOutEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockOutEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StockOutDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StockOutDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        <div class="accordion accordion_grid_row" id="accordionOrderManagement">--}}
{{--                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                <h6 class="accordion-header semi_accordion_collapse">--}}
{{--                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                        <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                               id="OrderManagementCheck">--}}
{{--                                                        <label class="form-check-label" for="OrderManagementCheck">--}}
{{--                                                            Order Management--}}
{{--                                                        </label>--}}
{{--                                                    </div>--}}
{{--                                                    <button class="accordion-button" type="button"--}}
{{--                                                            data-bs-toggle="collapse"--}}
{{--                                                            data-bs-target="#accordionOrderManagementCollapse"--}}
{{--                                                            aria-expanded="true"--}}
{{--                                                            aria-controls="accordionOrderManagementCollapse">--}}
{{--                                                    </button>--}}
{{--                                                </h6>--}}
{{--                                                <div id="accordionOrderManagementCollapse"--}}
{{--                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                    <div class="accordion-body custom_accordion_body">--}}
{{--                                                        <div class="accordion" id="accordionProduct">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="PendingCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="PendingCheck">--}}
{{--                                                                            Pending/Quotation--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionPendingCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionPendingCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionPendingCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PendingCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PendingCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PendingEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PendingEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PendingDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PendingDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionPurchased">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="PurchasedCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="PurchasedCheck">--}}
{{--                                                                            Purchased--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionPurchasedCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionPurchasedCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionPurchasedCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PurchasedCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PurchasedCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PurchasedEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PurchasedEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PurchasedDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PurchasedDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionCompleted">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="CompletedCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="CompletedCheck">--}}
{{--                                                                            Completed--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionCompletedCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionCompletedCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionCompletedCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="CompletedCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="CompletedCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="CompletedEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="CompletedEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="CompletedDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="CompletedDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        <div class="accordion accordion_grid_row" id="accordionIntegrations">--}}
{{--                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                <h6 class="accordion-header semi_accordion_collapse">--}}
{{--                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                        <input class="form-check-input" type="checkbox" value=""--}}
{{--                                                               id="IntegrationsCheck">--}}
{{--                                                        <label class="form-check-label" for="IntegrationsCheck">--}}
{{--                                                            Integrations--}}
{{--                                                        </label>--}}
{{--                                                    </div>--}}
{{--                                                    <button class="accordion-button" type="button"--}}
{{--                                                            data-bs-toggle="collapse"--}}
{{--                                                            data-bs-target="#accordionIntegrationsCollapse"--}}
{{--                                                            aria-expanded="true"--}}
{{--                                                            aria-controls="accordionIntegrationsCollapse">--}}
{{--                                                    </button>--}}
{{--                                                </h6>--}}
{{--                                                <div id="accordionIntegrationsCollapse"--}}
{{--                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                    <div class="accordion-body custom_accordion_body">--}}
{{--                                                        <div class="accordion" id="accordionQuickBooks">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="QuickBooksCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="QuickBooksCheck">--}}
{{--                                                                            QuickBooks--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionQuickBooksCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionQuickBooksCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionQuickBooksCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="QuickBooksView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="QuickBooksView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="QuickBooksCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="QuickBooksCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="QuickBooksEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="QuickBooksEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="QuickBooksDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="QuickBooksDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionPurchased">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="StripeCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="StripeCheck">--}}
{{--                                                                            Stripe--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionStripeCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionStripeCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionStripeCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StripeView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StripeView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StripeCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StripeCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StripeEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StripeEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StripeDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StripeDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionStax">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="StaxCheck">--}}
{{--                                                                        <label class="form-check-label" for="StaxCheck">--}}
{{--                                                                            Stax--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionStaxCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionStaxCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionStaxCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaxView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaxView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaxCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaxCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaxEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaxEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="StaxDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="StaxDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                        <div class="accordion" id="accordionPartyShipment">--}}
{{--                                                            <div class="accordion-item custom_accordion_items">--}}
{{--                                                                <h6 class="accordion-header">--}}
{{--                                                                    <div class="accordion_checkbox parent_checkbox">--}}
{{--                                                                        <input class="form-check-input" type="checkbox"--}}
{{--                                                                               value="" id="PartyShipmentCheck">--}}
{{--                                                                        <label class="form-check-label"--}}
{{--                                                                               for="PartyShipmentCheck">--}}
{{--                                                                            3rd party Shipment--}}
{{--                                                                        </label>--}}
{{--                                                                    </div>--}}
{{--                                                                    <button class="accordion-button" type="button"--}}
{{--                                                                            data-bs-toggle="collapse"--}}
{{--                                                                            data-bs-target="#accordionPartyShipmentCollapse"--}}
{{--                                                                            aria-expanded="true"--}}
{{--                                                                            aria-controls="accordionPartyShipmentCollapse">--}}
{{--                                                                    </button>--}}
{{--                                                                </h6>--}}
{{--                                                                <div id="accordionStaxCollapse"--}}
{{--                                                                     class="accordion-collapse collapse show custom_accordion_collapse">--}}
{{--                                                                    <div--}}
{{--                                                                        class="accordion-body custom_accordion_body custom_checkbox">--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PartyShipmentView">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PartyShipmentView">--}}
{{--                                                                                View--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PartyShipmentCreate">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PartyShipmentCreate">--}}
{{--                                                                                Create--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PartyShipmentEdit">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PartyShipmentEdit">--}}
{{--                                                                                Edit--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="accordion_checkbox">--}}
{{--                                                                            <input class="form-check-input"--}}
{{--                                                                                   type="checkbox" value=""--}}
{{--                                                                                   id="PartyShipmentDelete">--}}
{{--                                                                            <label class="form-check-label"--}}
{{--                                                                                   for="PartyShipmentDelete">--}}
{{--                                                                                Delete--}}
{{--                                                                            </label>--}}
{{--                                                                        </div>--}}
{{--                                                                    </div>--}}
{{--                                                                </div>--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection
@push('js')
    <script>

        $(document).ready(function() {
            var fitterId = {{ $fitter->FtrId }};

            var table = $('.customer-datatable').DataTable({
                processing: true,
                serverSide: true,
                pageLength: 10,
                ajax: {
                    url: `{{ route('fitter.customers', ['fitterId' => '__fitterId__']) }}`.replace('__fitterId__', fitterId),
                    type: 'GET',
                },
                columns: [
                    { data: 'sr_no', name: 'sr_no' },
                    { data: 'FirstNm', name: 'FirstNm' },
                    { data: 'EMailAddr', name: 'EMailAddr' },
                    { data: 'PhnNum', name: 'PhnNum' },
                    { data: 'total_orders', name: 'total_orders' },
                    { data: 'status', name: 'status' },
                    { data: 'action', name: 'action', orderable: false, searchable: false },
                ],
                order: [[0, 'asc']],
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

        });


        $(document).ready(function () {
            $("#uploadFile").on("change", function (event) {
                let file = event.target.files[0];
                let reader = new FileReader();

                if (file) {
                    reader.onload = function (e) {
                        $("#imagePreview").attr("src", e.target.result).show();
                        $("#uploadContent").hide();

                        $("#uploadFile").val("");
                    };
                    reader.readAsDataURL(file);
                }
            });

            $('.custom_accordion_items .accordion_checkbox.parent_checkbox input[type="checkbox"]').on('click', function () {
                const isChecked = $(this).prop('checked');
                $(this).closest('.custom_accordion_items').find('.custom_accordion_body input[type="checkbox"]').prop('checked', isChecked);
            });

            $('.custom_accordion_items .custom_accordion_body input[type="checkbox"]').on('click', function () {
                const parentCheckbox = $(this).closest('.custom_accordion_items').find('.accordion_checkbox.parent_checkbox input[type="checkbox"]');
                const childCheckboxes = $(this).closest('.custom_accordion_items').find('.custom_accordion_body input[type="checkbox"]');

                if (!$(this).prop('checked')) {
                    parentCheckbox.prop('checked', false);
                } else if (childCheckboxes.length === childCheckboxes.filter(':checked').length) {
                    parentCheckbox.prop('checked', true);
                }
            });
        });


    </script>
@endpush

