<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Loft extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'loft';
    protected $guarded = [];
    protected $primaryKey = 'LoftId';

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-loft-status"  loft_id="'.$this->LoftId.'" loft_status="0" >Active</span>' : '<span class="danger change-grip-size-status" loft_id="'.$this->LoftId.'" loft_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
