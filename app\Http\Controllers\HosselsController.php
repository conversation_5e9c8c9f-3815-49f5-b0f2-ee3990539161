<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ClbType;
use App\Models\Hossel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class HosselsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $hossels = Hossel::latest()->with('category')->get();
            return DataTables::of($hossels)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->id . '">';
                })
                ->addIndexColumn()
                ->addColumn('price', function ($row) {
                    return $row->price ?? '0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ?? 'N/A';
                })
                ->addColumn('category_id', function ($row) {
                    return $row->category->ClbTypeDsc ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item edit_hossel_button" href="javascript:void(0)"
                                       hossel_id="' . $row->id . '"
                                       hossel_name="' . $row->name . '"
                                       hossel_price="' . $row->price . '"
                                       hossel_category_id="' . $row->category_id . '">Edit</a></li>
                                <li>
                                <form action="' . route('hossels.destroy', $row->id) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['status','price','category_id','action','checkbox'])
                ->make(true);
        }
        $categories = ClbType::latest()->get();
        return view('dashboard.Cruds.Hossels.index', ['categories'=>$categories]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('dashboard.Cruds.Hossels.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  HosselRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'category_id' => 'required',
            'name' => 'required',
        ], [
            'category_id.required' => 'Please select category.',
            'name.required' => 'Hossel name is required.',
        ]);
        $hossel = new Hossel;
		$hossel->category_id = $request->input('category_id');
		$hossel->name = $request->input('name');
        $hossel->price = isset($request->price)  ? $request->price : 0;
        $hossel->save();
        return to_route('hossels.index');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $hossel = Hossel::findOrFail($id);
        return view('dashboard.Cruds.Hossels.show',['hossel'=>$hossel]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $hossel = Hossel::findOrFail($id);
        return view('dashboard.Cruds.Hossels.edit',['hossel'=>$hossel]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  HosselRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $hossel = Hossel::where('id', $request->hossel_id_update)->firstOrFail();
        extract($request->all());
        $request->validate([
            'category_id_update' => 'required',
            'name_update' => 'required',
        ], [
            'category_id_update.required' => 'Please select category.',
            'name_update.required' => 'Hossel name is required.',
        ]);
		$hossel->category_id = $request->input('category_id_update');
		$hossel->name = $request->input('name_update');
		$hossel->price = isset($request->price_update)  ? $request->price_update : 0;
        $hossel->save();

        return to_route('hossels.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $hossel = Hossel::findOrFail($id);
        $hossel->delete();

        return to_route('hossels.index');
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $gripSize = Hossel::where('id', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $gripSize->status = $request->status;
            $gripSize->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Hossel status updated successfully.',
                'status' => $gripSize->status,
                'resultHtml' => ($gripSize->status == 1) ? '<span style="cursor:pointer;" class="success change-hossel-status"  Hossel_id="'.$gripSize->id.'" Hossel_status="0" >Active</span>' : '<span class="danger change-grip-size-status" Hossel_id="'.$gripSize->id.'" Hossel_status="1"  style="cursor:pointer;">Inactive</span>'

            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update Hossel status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
