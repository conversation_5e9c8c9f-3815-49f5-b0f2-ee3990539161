<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shaft extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'shaft';
    protected $guarded = [];
    protected $appends = ['weights','flexes','materials','clubNumbers'];

    public function category(){
        return $this->HasOne(ClbType::class,'ClbTypeCd', 'category_id');
    }

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-shaft-status"  model_id="'.$this->id.'" model_status="0" >Active</span>' : '<span class="danger change-model-status" model_id="'.$this->id.'" model_status="1"  style="cursor:pointer;">Inactive</span>';
    }
    public function image()
    {
        try{
            return json_decode($this->images)[0];
        } catch (\Exception $e) {
            return 'shafts/default.png';
        }

    }
    public function images()
    {
        try{
            return json_decode($this->images);
        } catch (\Exception $e) {
            return ['shafts/default.png'];
        }
    }
    public function getweightsAttribute()
    {
        $headwgtrngIds = $this->parseJson($this->headwgtrng_ids);
        return HeadWgtrng::whereIn('HeadWgtRngCd', $headwgtrngIds)->pluck('HeadWgtRngDsc')->implode(', ');
    }
    public function getflexesAttribute()
    {
        $shfflxIds = $this->parseJson($this->shfflx_ids);
        return ShfFlx::whereIn('ShfFlxCd', $shfflxIds)->pluck('ShfFlxDsc')->implode(', ');
    }
    public function getmaterialsAttribute()
    {
        $shftypeIds = $this->parseJson($this->shftype_ids);
        return ShfType::whereIn('ShfTypeCd', $shftypeIds)->pluck('ShfTypeDsc')->implode(', ');
    }
    public function getclubNumbersAttribute()
    {
        return $this->parseJson($this->club_number);
    }
    // Helper method to parse JSON data
    private function parseJson($data)
    {
        try {
            return $data ? json_decode($data, true) : [];
        } catch (\Exception $e) {
            return [];
        }
    }
}
