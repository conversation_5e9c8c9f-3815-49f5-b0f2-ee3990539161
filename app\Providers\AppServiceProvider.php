<?php

namespace App\Providers;

use App\Models\Category;
use App\Models\ClbType;
use App\Models\Cust;
use App\Models\FaceAngle;
use App\Models\HeadDsgn;
use App\Models\HeadWgtrng;
use App\Models\Hossel;
use App\Models\Lie;
use App\Models\Loft;
use App\Models\Ord;
use App\Models\ShfFlx;
use App\Models\ShfLen;
use App\Models\ShfType;
use App\Models\GripType;
use App\Models\Ftr;
use App\Models\GripSz;
use App\Models\Staff;
use Illuminate\Support\ServiceProvider;
use App\Models\Crud;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (\Schema::hasTable('cruds')) {
            $crud = Crud::get();
            view()->share('crud', $crud);
        }

        $categoriesCount = ClbType::where('parent_category_id', '!=', null)->count();
        view()->share('categoriesCount', $categoriesCount);

        $modelsCount = HeadDsgn::count();
        view()->share('modelsCount', $modelsCount);

        $shaftsCount = ShfType::count();
        view()->share('shaftsCount', $shaftsCount);

        $gripTypesCount = GripType::count();
        view()->share('gripTypesCount', $gripTypesCount);

        $gripSizesCount = GripSz::count();
        view()->share('gripSizesCount', $gripSizesCount);

        $fitterCount = Ftr::count();
        view()->share('fitterCount', $fitterCount);

        $customersCount = Cust::count();
        view()->share('customersCount', $customersCount);

        $staffCount = Staff::count();
        view()->share('staffCount', $staffCount);

        $lieAngleCount = Lie::count();
        view()->share('lieAngleCount', $lieAngleCount);

        $faceAngleCount = FaceAngle::count();
        view()->share('faceAngleCount', $faceAngleCount);

        $shaftMaterialCount = ShfType::count();
        view()->share('shaftMaterialCount', $shaftMaterialCount);

        $shaftFlexCount = ShfFlx::count();
        view()->share('shaftFlexCount', $shaftFlexCount);

        $shaftWeightCount = HeadWgtrng::count();
        view()->share('shaftWeightCount', $shaftWeightCount);

        $loftCount = Loft::count();
        view()->share('loftCount', $loftCount);

        $shaftLenCount = ShfLen::count();
        view()->share('shaftLenCount', $shaftLenCount);

        $hosselCount = Hossel::count();
        view()->share('hosselCount', $hosselCount);

        $orderCounts = [
            'pending' => Ord::where('status', 'pending')->count(),
            'paid' => Ord::where('status', 'paid')->count(),
            'completed' => Ord::whereIn('status', ['completed','previous'])->count(),
        ];
        view()->share('orderCounts', $orderCounts);
    }
}





