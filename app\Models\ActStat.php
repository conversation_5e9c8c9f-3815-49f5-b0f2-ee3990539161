<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class ActStat extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'acctstat';
    protected $guarded = [];
    protected $primaryKey = 'AcctStatId';

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-acctstat-status"  acctstat_id="'.$this->AcctStatId.'" acctstat_status="0" >Active</span>' : '<span class="danger change-acctstat-status" acctstat_id="'.$this->AcctStatId.'" acctstat_status="1"  style="cursor:pointer;">Inactive</span>';
    }


    public function accounts()
    {
        return $this->hasMany(Acct::class, 'AcctStatId', 'AcctStatId');
    }

/*span.success
span.danger
span.yellow
text_success
text_danger
text_yellow*/

}
