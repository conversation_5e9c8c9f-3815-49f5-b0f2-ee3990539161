@extends('theme.layout.master')

@push('css')
@endpush

@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Completed</h5>
                            </div>
                            <div class="side_fields">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                                <!-- Filter Dropdown -->
                                <div class="dropdown-btn">
                                    <button type="button" class="btn dropdown-toggle light_green_btn" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                    <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                        <div class="dropdown_top">
                                            <h6 class="">Filter</h6>
                                            <button type="button" class="btn_close" data-bs-dismiss="dropdown" aria-label="Close">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <form>
                                            <div class="form-group">
                                                <label for="sku">SKU-ID</label>
                                                <select id="sku" class="form-control" name="sku">
                                                    <option value="">Select Order ID</option>
                                                    <option value="admin">145410</option>
                                                    <option value="user">145411</option>
                                                    <option value="manager">145412</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="order">Order ID</label>
                                                <select id="order" class="form-control" name="order">
                                                    <option value="">Select Order ID</option>
                                                    <option value="admin">145410</option>
                                                    <option value="user">145411</option>
                                                    <option value="manager">145412</option>
                                                </select>
                                            </div>
                                            <div class="dropdown_bottom">
                                                <button type="submit" class="btn light_green_btn">Apply Filter</button>
                                                <button type="button" class="btn cancel_btn" data-bs-dismiss="dropdown">Cancel</button>
                                            </div>
                                        </form>

                                    </div>
                                </div>
                            </div>
                        </div>

{{--                        <div class="table-responsive">--}}
{{--                            <table class="datatable table table-row-bordered gy-5">--}}
{{--                                <thead>--}}
{{--                                <tr>--}}
{{--                                    <th>SR#</th>--}}
{{--                                    <th>SKU-ID</th>--}}
{{--                                    <th>Order ID</th>--}}
{{--                                    <th>Customer Name</th>--}}
{{--                                    <th>Order Type</th>--}}
{{--                                    <th>Shipment ID</th>--}}
{{--                                    <th>Product Name</th>--}}
{{--                                    <th>Invoice</th>--}}
{{--                                    <th>Action</th>--}}
{{--                                </tr>--}}
{{--                                </thead>--}}
{{--                                <tbody>--}}
{{--                                @for ($i = 1; $i <= 10; $i++)--}}
{{--                                    <tr>--}}
{{--                                        <td>01</td>--}}
{{--                                        <td>145410</td>--}}
{{--                                        <td>145410</td>--}}
{{--                                        <td>John Doe</td>--}}
{{--                                        <td>Standard</td>--}}
{{--                                        <td>14524</td>--}}
{{--                                        <td>Name Here</td>--}}
{{--                                        <td>  <a href="javascript:void(0);" target="_blank"><i class="fa-solid fa-file-pdf text-danger"></i></a></td><td>--}}
{{--                                            <div class="dropdown">--}}
{{--                                                <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">--}}
{{--                                                    <img class="ellipsis_img" src="{{ asset('website') }}/assets/images/ellipsis.svg">--}}
{{--                                                </button>--}}
{{--                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">--}}
{{--                                                    <li><a class="dropdown-item" href="{{url('view-purchased')}}">View</a></li>--}}
{{--                                                    --}}{{--                                                    <li><a class="dropdown-item text-danger" href="{{url('edit-staff')}}">Edit</a></li>--}}
{{--                                                </ul>--}}
{{--                                            </div>--}}
{{--                                        </td>--}}
{{--                                    </tr>--}}
{{--                                @endfor--}}
{{--                                </tbody>--}}
{{--                            </table>--}}
{{--                        </div>--}}
                        <div class="table-responsive">
                            <table class="pending-orders-datatable table table-row-bordered gy-5">
                                <thead>
                                <tr>
                                    <th>SR#</th>
                                    <th>Order No</th>
                                    <th>Customer</th>
                                    <th>Fitter</th>
                                    <th>Total Qty</th>
                                    <th>Total Price</th>
                                    <th>Order Type</th>
                                    <th>Bill Type</th>
                                    <th>Ship Via</th>
                                    <th>Current Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection

@push('js')
    <script>
        $(document).ready(function () {
            var status = "{{ $status }}";

            var table = $('.pending-orders-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('orders.index', ['status' => '']) }}' + '/' + status,
                    type: 'GET',
                    dataSrc: 'data'
                },
                columns: [
                    {data: null, name: null, defaultContent: ''},
                    {data: 'OrdNo', name: 'OrdNo'},
                    {data: 'customer', name: 'customer'},
                    {data: 'fitter', name: 'fitter'},
                    {data: 'total_qty', name: 'total_qty'},
                    {data: 'total_price', name: 'total_price'},
                    {data: 'order_type', name: 'order_type'},
                    {data: 'bill_type', name: 'bill_type'},
                    {data: 'ship_via', name: 'ship_via'},
                    {data: 'current_status', name: 'current_status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(0)').html(dataIndex + 1);
                },
                initComplete: function () {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

        });

    </script>
@endpush
