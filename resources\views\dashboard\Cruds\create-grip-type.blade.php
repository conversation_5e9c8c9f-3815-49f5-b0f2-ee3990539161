@extends('theme.layout.master')

@push('css')
@endpush

@section('content')
    <section class="create_grip">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('grip-types.store') }}" method="POST" enctype="multipart/form-data" id="grip_types_form">
                        @csrf
                        <div class="white_box">
                            <h4>Create Grip Type</h4>
                            <div class="product_specification">
                                <div class="white_box form_gap">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Grip Type *</label>
                                                <input type="text" class="form-control" id="grip_type_input" name="grip_type" placeholder="Type Here">
                                                @error('grip_type')
                                                    <span class="invalid-feedback">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Grip Price</label>
                                                <input type="text" class="form-control" id="grip_price_input" name="grip_price" placeholder="Type Here" value="{{ old('grip_price') }}"  oninput="formatPrice(this)">
                                                @error('grip_price')
                                                    <span class="invalid-feedback">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="submit_form_btn">
                                <button type="submit" class="btn light_green_btn">Submit</button>
                                <button type="button" class="btn cancel_btn go_back">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script>
        $(document).ready(function () {
            $('#grip_types_form').on('submit', function (e) {
                // Remove previous errors
                $('.invalid-feedback').remove();
                $('#grip_type_input').removeClass('is-invalid');

                let gripType = $('#grip_type_input').val().trim();
                let isValid = true;

                if (gripType === '') {
                    $('#grip_type_input')
                        .addClass('is-invalid')
                        .after('<div class="invalid-feedback d-block"><strong>Grip type is required.</strong></div>');
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault(); // Stop form from submitting
                }
            });
        });
    </script>
@endpush
